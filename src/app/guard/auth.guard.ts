import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
import { AuthService } from '../services/auth.service';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(private authService: AuthService, private router: Router) {}

  canActivate(
    next: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    const requiresAuth = next.data['requiresAuth'] !== false;
    const isAuthenticated = this.authService.isAuthenticated();

    // Si la route nécessite une authentification
    if (requiresAuth) {
      if (!isAuthenticated) {
        // Rediriger vers la page de connexion avec l'URL de retour
        return this.router.createUrlTree(['/auth/login'], {
          queryParams: { returnUrl: state.url },
        });
      }
      return true;
    }

    // Si la route ne nécessite pas d'authentification (login/register)
    if (isAuthenticated) {
      // Si l'utilisateur est déjà connecté, rediriger vers la page d'accueil
      return this.router.createUrlTree(['/']);
    }
    return true;
  }
}
