import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { Router } from '@angular/router';

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
}

export interface UserProfile {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  role: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SignInDto {
  email: string;
  password: string;
}

export interface SignUpDto {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone?: string;
}

export interface ResetPasswordRequestDto {
  email: string;
}

export interface ValidateOtpDto {
  otp: string;
}

export interface ResetPasswordDto {
  otp: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateProfileDto {
  firstName?: string;
  lastName?: string;
  phone?: string;
}

@Injectable({
  providedIn: 'root',
})
export class AuthService {
  private apiUrl = `${environment.BASE_URL}/auth`;
  private currentUserSubject = new BehaviorSubject<UserProfile | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient, private router: Router) {
    this.loadUserFromStorage();
  }

  private getAuthHeaders(): HttpHeaders {
    const token = this.getAccessToken();
    return new HttpHeaders({
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    });
  }

  private loadUserFromStorage(): void {
    const user = localStorage.getItem('currentUser');
    if (user) {
      this.currentUserSubject.next(JSON.parse(user));
    }
  }

  // Méthodes d'authentification
  signIn(credentials: SignInDto): Observable<TokenPair> {
    return this.http.post<TokenPair>(`${this.apiUrl}/signin`, credentials).pipe(
      tap((tokens) => {
        this.storeTokens(tokens);
        this.fetchUserProfile().subscribe();
      })
    );
  }

  signUp(userData: SignUpDto): Observable<TokenPair> {
    return this.http.post<TokenPair>(`${this.apiUrl}/signup`, userData).pipe(
      tap((tokens) => {
        this.storeTokens(tokens);
        this.fetchUserProfile().subscribe();
      })
    );
  }

  signOut(): void {
    this.clearAuthData();
    this.router.navigate(['/auth/login']);
  }

  // Gestion des tokens
  storeTokens(tokens: TokenPair): void {
    localStorage.setItem('accessToken', tokens.accessToken);
    localStorage.setItem('refreshToken', tokens.refreshToken);
  }

  getAccessToken(): string | null {
    return localStorage.getItem('accessToken');
  }

  getRefreshToken(): string | null {
    return localStorage.getItem('refreshToken');
  }

  clearAuthData(): void {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('currentUser');
    this.currentUserSubject.next(null);
  }

  // Gestion du profil utilisateur
  fetchUserProfile(): Observable<UserProfile> {
    return this.http
      .get<UserProfile>(`${this.apiUrl}/me`, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap((user) => {
          this.currentUserSubject.next(user);
          localStorage.setItem('currentUser', JSON.stringify(user));
        })
      );
  }

  updateUserProfile(profileData: UpdateProfileDto): Observable<UserProfile> {
    return this.http
      .put<UserProfile>(`${this.apiUrl}/profile`, profileData, {
        headers: this.getAuthHeaders(),
      })
      .pipe(
        tap((user) => {
          this.currentUserSubject.next(user);
          localStorage.setItem('currentUser', JSON.stringify(user));
        })
      );
  }

  // Réinitialisation de mot de passe
  requestPasswordReset(email: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/password-reset/request`, {
      email,
    });
  }

  validateResetOtp(otp: string): Observable<void> {
    return this.http.post<void>(`${this.apiUrl}/password-reset/validate-otp`, {
      otp,
    });
  }

  resetPassword(resetData: ResetPasswordDto): Observable<void> {
    return this.http.post<void>(
      `${this.apiUrl}/password-reset/reset`,
      resetData
    );
  }

  // Rafraîchissement du token
  refreshTokens(): Observable<TokenPair> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    return this.http
      .post<TokenPair>(
        `${this.apiUrl}/refresh-tokens`,
        {},
        {
          headers: new HttpHeaders({
            Authorization: `Bearer ${refreshToken}`,
          }),
        }
      )
      .pipe(
        tap((tokens) => {
          this.storeTokens(tokens);
        })
      );
  }

  // Vérification du compte
  verifyAccount(otp: string): Observable<void> {
    return this.http
      .post<void>(
        `${this.apiUrl}/verify-account`,
        { otp },
        {
          headers: this.getAuthHeaders(),
        }
      )
      .pipe(
        tap(() => {
          // Mettre à jour le statut de vérification de l'utilisateur
          const currentUser = this.currentUserSubject.value;
          if (currentUser) {
            const updatedUser = { ...currentUser, isVerified: true };
            this.currentUserSubject.next(updatedUser);
            localStorage.setItem('currentUser', JSON.stringify(updatedUser));
          }
        })
      );
  }

  // Vérification de l'état d'authentification
  isAuthenticated(): boolean {
    return !!this.getAccessToken();
  }

  // Vérification des rôles
  hasRole(role: string): boolean {
    const user = this.currentUserSubject.value;
    return user?.role === role;
  }

  // Vérification si l'email est vérifié
  isEmailVerified(): boolean {
    const user = this.currentUserSubject.value;
    return user?.isVerified || false;
  }
}
