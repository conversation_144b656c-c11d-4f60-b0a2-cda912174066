import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { environment } from '../../environments/environment';

export interface JobOffer {
  id: string;
  title: string;
  description: string;
  publishDate: string;
  expirationDate: string;
  status: 'DRAFT' | 'ACTIVE' | 'EXPIRED' | 'FILLED' | 'CANCELLED';
  location: string;
  contractTypes: ContractType[];
  minSalary?: number;
  maxSalary?: number;
  requiredSkills: string[];
  company: {
    id: string;
    companyName: string;
    logo?: string;
    email: string;
    website?: string;
    address?: {
      street?: string;
      city?: string;
      country: string;
    };
  };
  department?: {
    id: string;
    departmentName: string;
  };
  position?: {
    id: string;
    positionTitle: string;
  };
}

export interface CreateApplicationDto {
  jobId: string;
  coverLetter: string;
  resume: string;
  references?: string;
  additionalDocuments?: string[];
  preferredStartDate?: string;
  currentEmploymentStatus?: string;
  desiredSalary?: number;
}

export interface Application {
  id: string;
  applicationDate: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  jobId: string;
  userId: string;
  coverLetter: string;
  resume: string;
  references?: string;
  additionalDocuments: string[];
  preferredStartDate?: string;
  currentEmploymentStatus?: string;
  desiredSalary?: number;
  jobOffer: JobOffer;
}

export type ContractType =
  | 'FULL_TIME'
  | 'PART_TIME'
  | 'TEMPORARY'
  | 'CONTRACT'
  | 'INTERNSHIP'
  | 'REMOTE'
  | 'HYBRID'
  | 'ON_SITE'
  | 'SEASONAL'
  | 'FREELANCE';

@Injectable({
  providedIn: 'root',
})
export class JobOffersService {
  private apiUrl = `${environment.BASE_URL}`;
  private jobOffersSubject = new BehaviorSubject<JobOffer[]>([]);
  public jobOffers$ = this.jobOffersSubject.asObservable();

  constructor(private http: HttpClient) {}

  private getAuthHeaders(): HttpHeaders {
    const token = localStorage.getItem('authToken');
    return new HttpHeaders({
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    });
  }

  // Récupérer toutes les offres d'emploi publiques
  getAllJobOffers(): Observable<JobOffer[]> {
    return this.http.get<JobOffer[]>(
      `${this.apiUrl}/companies/public/job-offers`
    );
  }

  // Récupérer une offre d'emploi par ID
  getJobOfferById(id: string): Observable<JobOffer> {
    return this.http.get<JobOffer>(
      `${this.apiUrl}/companies/public/job-offers/${id}`
    );
  }

  // Récupérer les offres d'emploi d'une entreprise
  getJobOffersByCompany(companyId: string): Observable<JobOffer[]> {
    return this.http.get<JobOffer[]>(
      `${this.apiUrl}/companies/${companyId}/job-offers`
    );
  }

  // Créer une nouvelle offre d'emploi (pour les entreprises)
  createJobOffer(
    companyId: string,
    jobOffer: Partial<JobOffer>
  ): Observable<JobOffer> {
    return this.http.post<JobOffer>(
      `${this.apiUrl}/companies/${companyId}/job-offers`,
      jobOffer,
      { headers: this.getAuthHeaders() }
    );
  }

  // Mettre à jour une offre d'emploi
  updateJobOffer(
    companyId: string,
    id: string,
    jobOffer: Partial<JobOffer>
  ): Observable<JobOffer> {
    return this.http.patch<JobOffer>(
      `${this.apiUrl}/companies/${companyId}/job-offers/${id}`,
      jobOffer,
      { headers: this.getAuthHeaders() }
    );
  }

  // Supprimer une offre d'emploi
  deleteJobOffer(companyId: string, id: string): Observable<void> {
    return this.http.delete<void>(
      `${this.apiUrl}/companies/${companyId}/job-offers/${id}`,
      { headers: this.getAuthHeaders() }
    );
  }

  // Sauvegarder une offre d'emploi
  saveJobOffer(companyId: string, id: string): Observable<any> {
    return this.http.post(
      `${this.apiUrl}/companies/${companyId}/job-offers/${id}/save`,
      {},
      { headers: this.getAuthHeaders() }
    );
  }

  // Récupérer les offres sauvegardées
  getSavedJobOffers(): Observable<JobOffer[]> {
    return this.http.get<JobOffer[]>(`${this.apiUrl}/companies/saved`, {
      headers: this.getAuthHeaders(),
    });
  }

  // Créer une candidature
  createApplication(
    application: CreateApplicationDto
  ): Observable<Application> {
    // On utilise un companyId générique ou on le récupère depuis l'offre
    return this.http.post<Application>(
      `${this.apiUrl}/applications`,
      application,
      { headers: this.getAuthHeaders() }
    );
  }

  // Récupérer mes candidatures
  getMyApplications(): Observable<Application[]> {
    return this.http.get<Application[]>(`${this.apiUrl}/applications/me`, {
      headers: this.getAuthHeaders(),
    });
  }

  // Récupérer une candidature par ID
  getApplicationById(id: string): Observable<Application> {
    return this.http.get<Application>(`${this.apiUrl}/applications/${id}`, {
      headers: this.getAuthHeaders(),
    });
  }

  // Mettre à jour une candidature
  updateApplication(
    id: string,
    application: Partial<CreateApplicationDto>
  ): Observable<Application> {
    return this.http.patch<Application>(
      `${this.apiUrl}/applications/${id}`,
      application,
      { headers: this.getAuthHeaders() }
    );
  }

  // Supprimer une candidature
  deleteApplication(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/applications/${id}`, {
      headers: this.getAuthHeaders(),
    });
  }

  // Méthodes utilitaires pour le filtrage côté client
  filterJobsByCategory(jobs: JobOffer[], categoryId: number): JobOffer[] {
    if (categoryId === 0) return jobs;

    // Mapping des catégories selon votre logique métier
    const categoryMappings: { [key: number]: string[] } = {
      1: ['Développeur', 'Programmeur', 'Tech', 'IT'], // Technologie
      2: ['Finance', 'Comptable', 'Banking'], // Finance
      3: ['Santé', 'Médecin', 'Infirmier'], // Santé
      4: ['Marketing', 'Communication', 'Digital'], // Marketing
      5: ['Design', 'UI/UX', 'Graphique'], // Design
    };

    const keywords = categoryMappings[categoryId] || [];
    return jobs.filter((job) =>
      keywords.some(
        (keyword) =>
          job.title.toLowerCase().includes(keyword.toLowerCase()) ||
          job.description.toLowerCase().includes(keyword.toLowerCase())
      )
    );
  }

  searchJobs(jobs: JobOffer[], searchTerm: string): JobOffer[] {
    if (!searchTerm.trim()) return jobs;

    const term = searchTerm.toLowerCase();
    return jobs.filter(
      (job) =>
        job.title.toLowerCase().includes(term) ||
        job.description.toLowerCase().includes(term) ||
        job.company.companyName.toLowerCase().includes(term) ||
        job.location.toLowerCase().includes(term) ||
        job.requiredSkills.some((skill) => skill.toLowerCase().includes(term))
    );
  }

  // Convertir les types de contrat pour l'affichage
  getContractTypeLabel(contractType: ContractType): string {
    const labels: { [key in ContractType]: string } = {
      FULL_TIME: 'Temps plein',
      PART_TIME: 'Temps partiel',
      TEMPORARY: 'Temporaire',
      CONTRACT: 'Contrat',
      INTERNSHIP: 'Stage',
      REMOTE: 'Télétravail',
      HYBRID: 'Hybride',
      ON_SITE: 'Sur site',
      SEASONAL: 'Saisonnier',
      FREELANCE: 'Freelance',
    };
    return labels[contractType] || contractType;
  }

  // Formater la date de publication
  formatPublishDate(publishDate: string): string {
    const date = new Date(publishDate);
    const now = new Date();

    // Vérifier si la date est valide
    if (isNaN(date.getTime())) {
      return 'Date invalide';
    }

    // Calculer la différence en millisecondes (sans Math.abs pour gérer les dates futures)
    const diffTime = now.getTime() - date.getTime();

    // Si la date est dans le futur
    if (diffTime < 0) {
      return 'Dans le futur';
    }

    // Convertir en différentes unités
    const diffMinutes = Math.floor(diffTime / (1000 * 60));
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const diffWeeks = Math.floor(diffDays / 7);
    const diffMonths = Math.floor(diffDays / 30);
    const diffYears = Math.floor(diffDays / 365);

    // Retourner le format approprié
    if (diffMinutes < 1) return "À l'instant";
    if (diffMinutes < 60)
      return `il y a ${diffMinutes} minute${diffMinutes > 1 ? 's' : ''}`;
    if (diffHours < 24)
      return `il y a ${diffHours} heure${diffHours > 1 ? 's' : ''}`;
    if (diffDays === 1) return 'il y a 1 jour';
    if (diffDays < 7) return `il y a ${diffDays} jours`;
    if (diffWeeks === 1) return 'il y a 1 semaine';
    if (diffDays < 30)
      return `il y a ${diffWeeks} semaine${diffWeeks > 1 ? 's' : ''}`;
    if (diffMonths === 1) return 'il y a 1 mois';
    if (diffDays < 365) return `il y a ${diffMonths} mois`;
    if (diffYears === 1) return 'il y a 1 an';
    return `il y a ${diffYears} an${diffYears > 1 ? 's' : ''}`;
  }

  // Formater le salaire
  formatSalary(minSalary?: number, maxSalary?: number): string {
    if (!minSalary && !maxSalary) return 'Salaire compétitif';
    if (minSalary && maxSalary) return `${minSalary}$ - ${maxSalary}$`;
    if (minSalary) return `À partir de ${minSalary}$`;
    if (maxSalary) return `Jusqu'à ${maxSalary}$`;
    return 'Salaire compétitif';
  }
}
