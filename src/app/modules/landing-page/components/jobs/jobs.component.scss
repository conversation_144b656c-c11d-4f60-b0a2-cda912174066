@import "../../../../../assets/scss/abstracts/variables";

.jobs-section {
  background: #fff;
  padding: 4rem 0 2rem 0;
  .container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }
  .section-header {
    text-align: center;
    margin-bottom: 2.5rem;
    .section-title {
      font-size: 2.2rem;
      font-weight: 800;
      color: #222;
      margin-bottom: 0.7rem;
      font-family: "Inter", "<PERSON><PERSON>", Arial, sans-serif;
    }
    .section-subtitle {
      color: #666;
      font-size: 1.1rem;
      margin-bottom: 0;
    }
  }
  .job-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    background: #f9f9fb;
    border-radius: 1.2rem;
    box-shadow: 0 2px 12px rgba(139, 92, 246, 0.06);
    padding: 1.2rem 1.5rem;
    margin-bottom: 2.2rem;
    align-items: center;
    .filter-tabs {
      display: flex;
      gap: 1rem;
      .filter-tab {
        background: var(--primary-light);
        color: var(--primary);
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1.2rem;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: background 0.2s, color 0.2s;
        &.active,
        &:hover {
          background: linear-gradient(
            90deg,
            var(--primary) 0%,
            var(--primary-dark) 100%
          );
          color: #fff;
        }
      }
    }
    .search-box {
      flex: 1;
      display: flex;
      align-items: center;
      input {
        width: 100%;
        padding: 0.6rem 1rem;
        border-radius: 8px;
        border: 1px solid #ececec;
        font-size: 1rem;
        color: #222;
        background: #fff;
        transition: border 0.2s;
        &:focus {
          border: 1px solid #8b5cf6;
          outline: none;
        }
      }
    }
  }
  .job-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 2.2rem;
    margin-bottom: 2.5rem;
  }
  .job-card {
    background: #f9f9fb;
    border-radius: 1.2rem;
    box-shadow: 0 2px 12px rgba(139, 92, 246, 0.06);
    padding: 2.2rem 1.5rem 1.5rem 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: box-shadow 0.2s;
    &:hover {
      box-shadow: 0 6px 24px rgba(34, 91, 246, 0.13);
    }
    .job-badge {
      background: var(--primary-light);
      color: var(--primary);
      font-size: 0.85rem;
      font-weight: 600;
      border-radius: 12px;
      padding: 0.3rem 0.9rem;
      letter-spacing: 0.03em;
      margin-bottom: 1rem;
      display: inline-block;
    }
    .job-main {
      .job-header {
        margin-bottom: 0.7rem;
        .job-title {
          font-size: 1.15rem;
          font-weight: 700;
          color: #222;
          margin-bottom: 0.2rem;
        }
        .job-date {
          color: #888;
          font-size: 0.95rem;
        }
      }
      .company-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 0.7rem;
        .company-logo {
          width: 38px;
          height: 38px;
          border-radius: 50%;
          background: #ece9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          img {
            width: 28px;
            height: 28px;
            object-fit: contain;
            border-radius: 50%;
          }
        }
        .company-details {
          h4 {
            font-size: 1rem;
            font-weight: 600;
            color: #8b5cf6;
            margin: 0 0 0.2rem 0;
          }
          .job-meta {
            display: flex;
            gap: 0.7rem;
            font-size: 0.97rem;
            color: #555;
          }
        }
      }
      .job-skills {
        margin-top: 0.5rem;
        .skill-tag,
        .skill-tag-more {
          background: var(--primary-light);
          color: var(--primary);
          border-radius: 8px;
          padding: 0.2rem 0.7rem;
          font-size: 0.95rem;
          font-weight: 500;
          margin-right: 0.4rem;
          margin-bottom: 0.2rem;
          display: inline-block;
        }
      }
    }
    .job-footer {
      display: flex;
      gap: 1rem;
      margin-top: 1.2rem;
      .apply-btn,
      .details-btn {
        background: linear-gradient(
          90deg,
          var(--primary) 0%,
          var(--primary-dark) 100%
        );
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 0.7rem 1.5rem;
        font-weight: 600;
        font-size: 1rem;
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.08);
        transition: background 0.2s, box-shadow 0.2s;
        cursor: pointer;
        &:hover {
          background: linear-gradient(
            90deg,
            var(--primary) 0%,
            var(--primary-dark) 100%
          );
          box-shadow: 0 4px 16px rgba(139, 92, 246, 0.12);
        }
      }
      .btn-primary {
        background: linear-gradient(
          90deg,
          var(--primary) 0%,
          var(--primary-dark) 100%
        );
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 0.7rem 1.5rem;
        font-weight: 600;
        font-size: 1rem;
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.08);
        transition: background 0.2s, box-shadow 0.2s;
        cursor: pointer;
        &:hover {
          background: var(--primary-dark);
          box-shadow: 0 4px 16px rgba(139, 92, 246, 0.12);
        }
      }
      .btn-outline {
        border: 1.5px solid var(--primary);
        color: var(--primary);
        background: #fff;
        &:hover {
          background: var(--primary-light);
        }
      }
    }
  }
  .loading-state,
  .empty-state {
    text-align: center;
    color: #888;
    font-size: 1.1rem;
    margin: 2rem 0;
  }
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    .custom-pagination {
      .ngx-pagination {
        display: flex;
        gap: 0.5rem;
        .current {
          background: #8b5cf6;
          color: #fff;
          border-radius: 6px;
          padding: 0.4rem 0.9rem;
        }
        a,
        span {
          color: #8b5cf6;
          border-radius: 6px;
          padding: 0.4rem 0.9rem;
          transition: background 0.2s, color 0.2s;
          &:hover {
            background: #ece9fa;
            color: #8b5cf6;
          }
        }
      }
    }
  }
}

.bg-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  pointer-events: none;

  .bg-circle {
    position: absolute;
    border-radius: 50%;
    filter: blur(80px);
    opacity: 0.08;
    z-index: 1;

    &.circle-1 {
      width: 400px;
      height: 400px;
      background: $primary;
      top: 20%;
      left: 10%;
      animation: float 8s infinite ease-in-out;
    }

    &.circle-2 {
      width: 300px;
      height: 300px;
      background: #2563eb;
      bottom: 10%;
      right: 15%;
      animation: float 10s infinite ease-in-out 2s;
    }
  }

  .bg-grid {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: linear-gradient(rgba($primary, 0.02) 1px, transparent 1px),
      linear-gradient(90deg, rgba($primary, 0.02) 1px, transparent 1px);
    background-size: 40px 40px;
    z-index: 2;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(2deg);
  }
}
/* Styles pour les modales */
.modal-content {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.modal-header {
  background: linear-gradient(90deg, $primary 0%, $primary-dark 100%);
  color: white;
  border-bottom: none;
  padding: 1.5rem;

  .modal-title {
    font-weight: 600;
  }

  .close {
    color: white;
    text-shadow: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }
  }
}

.modal-body {
  padding: 2rem;

  .company-info {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba($text-light, 0.2);
    margin-bottom: 1.5rem;

    .company-logo {
      width: 80px;
      height: 80px;
      border-radius: 12px;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      box-shadow: 0 3px 15px rgba(0, 0, 0, 0.05);
      border: 1px solid rgba($primary, 0.1);

      img {
        width: 80%;
        height: 80%;
        object-fit: contain;
      }
    }

    h5 {
      font-size: 1.4rem;
      margin-bottom: 0.5rem;
    }
  }

  .job-meta {
    p {
      margin-bottom: 0.8rem;
      font-size: 0.95rem;

      strong {
        color: $text-dark;
        font-weight: 600;
      }
    }
  }

  .job-description {
    h5 {
      color: $text-dark;
      font-size: 1.2rem;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    p {
      color: $text-medium;
      line-height: 1.7;
      white-space: pre-line;
    }
  }

  .job-skills {
    h5 {
      color: $text-dark;
      font-size: 1.2rem;
      margin-bottom: 1rem;
      font-weight: 600;
    }

    .skills-container {
      display: flex;
      flex-wrap: wrap;
      gap: 0.8rem;

      .skill-tag {
        background: rgba($primary, 0.1);
        color: $primary-dark;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.85rem;
        font-weight: 500;
      }
    }
  }

  .form-group {
    margin-bottom: 1.5rem;

    label {
      font-weight: 500;
      color: $text-dark;
      margin-bottom: 0.5rem;
      display: block;
    }

    .form-control {
      border-radius: 8px;
      border: 1px solid rgba($text-light, 0.3);
      padding: 0.75rem 1rem;
      transition: all 0.3s ease;

      &:focus {
        border-color: $primary;
        box-shadow: 0 0 0 3px rgba($primary, 0.1);
      }
    }

    textarea.form-control {
      min-height: 120px;
    }
  }
}

.modal-footer {
  border-top: 1px solid rgba($text-light, 0.2);
  padding: 1.5rem;

  .btn {
    padding: 0.7rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #6b7280;

    &:hover {
      background: rgba($text-light, 0.2);
    }
  }

  .btn-primary {
    background: linear-gradient(
      90deg,
      var(--primary) 0%,
      var(--primary-dark) 100%
    );
    border: none;
    color: white;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($primary, 0.3);
    }
  }
}

.job-type-badge {
  display: inline-block;
  margin-left: 0.7rem;
  margin-bottom: 0.2rem;
  padding: 0.25rem 0.9rem;
  font-size: 0.98rem;
  font-weight: 600;
  border-radius: 16px;
  background: var(--primary-light);
  color: var(--primary);
  letter-spacing: 0.02em;
  box-shadow: none;
  vertical-align: middle;
}

.job-title-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.2rem;
}
