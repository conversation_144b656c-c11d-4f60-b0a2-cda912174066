import { Component, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';

interface Talent {
  title: string;
  description: string;
  rating: number;
  images: string[];
}

@Component({
  selector: 'app-features',
  templateUrl: './features.component.html',
  styleUrls: ['./features.component.scss'],
})
export class FeaturesComponent implements AfterViewInit {
  talents: Talent[] = [
    {
      title: 'Spécialistes RH',
      description:
        'Experts en gestion des ressources humaines, recrutement et développement organisationnel.',
      rating: 4.8,
      images: [
        'assets/images/talents/hr-specialist-1.jpg',
        'assets/images/talents/hr-specialist-2.jpg',
        'assets/images/talents/hr-specialist-3.jpg',
      ],
    },
    {
      title: 'Consultants en Recrutement',
      description:
        "Professionnels spécialisés dans l'identification et l'acquisition de talents.",
      rating: 4.8,
      images: [
        'assets/images/talents/recruiter-1.jpg',
        'assets/images/talents/recruiter-2.jpg',
        'assets/images/talents/recruiter-3.jpg',
      ],
    },
    {
      title: 'Analystes RH',
      description:
        'Experts en analyse de données RH et reporting pour optimiser les performances.',
      rating: 4.9,
      images: [
        'assets/images/talents/analyst-1.jpg',
        'assets/images/talents/analyst-2.jpg',
        'assets/images/talents/analyst-3.jpg',
      ],
    },
    {
      title: 'Formateurs & Coachs',
      description:
        'Spécialistes en développement des compétences et formation professionnelle.',
      rating: 4.7,
      images: [
        'assets/images/talents/trainer-1.jpg',
        'assets/images/talents/trainer-2.jpg',
        'assets/images/talents/trainer-3.jpg',
      ],
    },
    {
      title: 'Gestionnaires de Paie',
      description:
        'Experts en administration de la paie et conformité réglementaire.',
      rating: 4.9,
      images: [
        'assets/images/talents/payroll-1.jpg',
        'assets/images/talents/payroll-2.jpg',
        'assets/images/talents/payroll-3.jpg',
      ],
    },
    {
      title: 'Consultants SIRH',
      description:
        "Spécialistes en systèmes d'information RH et transformation digitale.",
      rating: 4.8,
      images: [
        'assets/images/talents/sirh-1.jpg',
        'assets/images/talents/sirh-2.jpg',
        'assets/images/talents/sirh-3.jpg',
      ],
    },
    {
      title: 'Experts en Diversité',
      description:
        'Consultants spécialisés dans les stratégies de diversité et inclusion.',
      rating: 4.9,
      images: [
        'assets/images/talents/diversity-1.jpg',
        'assets/images/talents/diversity-2.jpg',
        'assets/images/talents/diversity-3.jpg',
      ],
    },
    {
      title: 'Psychologues du Travail',
      description:
        'Professionnels en psychologie organisationnelle et bien-être au travail.',
      rating: 4.8,
      images: [
        'assets/images/talents/psychologist-1.jpg',
        'assets/images/talents/psychologist-2.jpg',
        'assets/images/talents/psychologist-3.jpg',
      ],
    },
    {
      title: 'Experts en IA RH',
      description:
        'Spécialistes en intelligence artificielle appliquée aux ressources humaines.',
      rating: 4.8,
      images: [
        'assets/images/talents/ai-expert-1.jpg',
        'assets/images/talents/ai-expert-2.jpg',
        'assets/images/talents/ai-expert-3.jpg',
      ],
    },
  ];

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngAfterViewInit() {
    const cards = this.el.nativeElement.querySelectorAll('.talent-card');
    const section = this.el.nativeElement.querySelector(
      '.talent-showcase-section'
    );
    if (!cards.length || !section) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            cards.forEach((card: HTMLElement, i: number) => {
              setTimeout(() => {
                this.renderer.addClass(card, 'visible');
              }, i * 150);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.2 }
    );
    observer.observe(section);
  }
}
