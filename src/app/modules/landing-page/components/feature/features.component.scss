// === SECTION SHOWCASE TALENTS STYLE UPWORK ===
.talent-showcase-section {
  background: white;
  padding: 4rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === HEADER DE SECTION ===
  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 3rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
        margin-bottom: 2rem;
      }
    }

    .stats-overview {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      max-width: 900px;
      margin: 0 auto;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .stat-item {
        display: flex;
        align-items: center;
        gap: 1rem;

        @media (max-width: 768px) {
          justify-content: center;
          text-align: center;
        }

        .stat-icon {
          flex-shrink: 0;
        }

        .stat-content {
          .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.25rem;
            line-height: 1;

            @media (max-width: 768px) {
              font-size: 1.25rem;
            }
          }

          .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            line-height: 1.4;
          }
        }
      }
    }
  }

  // === GRILLE DES TALENTS ===
  .talents-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 3rem;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .talent-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 1.5rem;
      transition: all 0.2s ease;
      opacity: 0;
      transform: translateY(20px);

      &:hover {
        border-color: #d1d5db;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      }

      &.visible {
        opacity: 1;
        transform: translateY(0);
      }

      .talent-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;

        .talent-images {
          position: relative;
          height: 40px;
          width: 80px;

          .talent-image {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid white;
            object-fit: cover;
          }
        }

        .talent-rating {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          font-size: 0.875rem;

          .rating-value {
            font-weight: 600;
            color: #111827;
          }

          .rating-label {
            color: #6b7280;
          }
        }
      }

      .talent-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.5rem;
        line-height: 1.3;
      }

      .talent-description {
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.5;
        margin-bottom: 1rem;
      }

      .talent-link {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: #f3f4f6;
        border-radius: 50%;
        color: #6b7280;
        transition: all 0.2s ease;

        &:hover {
          background: #2563eb;
          color: white;
        }
      }
    }
  }

  // === FOOTER DE SECTION ===
  .section-footer {
    text-align: center;

    .btn {
      display: inline-flex;
      align-items: center;
      padding: 0.75rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: 6px;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &.btn-outline {
        color: #374151;
        border-color: #d1d5db;
        background: white;

        &:hover {
          border-color: #9ca3af;
          color: #111827;
        }
      }
    }
  }
}
