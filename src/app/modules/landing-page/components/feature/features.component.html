<section class="talent-showcase-section" id="talents">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Experts RH de confiance et à distance</h2>
      <div class="stats-overview">
        <div class="stat-item">
          <div class="stat-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
                fill="#2563eb"
              />
            </svg>
          </div>
          <div class="stat-content">
            <div class="stat-number">4.91</div>
            <div class="stat-label">
              Note moyenne pour le travail avec les talents RH.
            </div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-content">
            <div class="stat-number">211K+</div>
            <div class="stat-label">
              contrats impliquant du travail RH et de recrutement l'année
              dernière.
            </div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-content">
            <div class="stat-number">1,665</div>
            <div class="stat-label">
              compétences représentées par les talents sur CompulseHR.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Grille des talents style Upwork -->
    <div class="talents-grid">
      <div
        class="talent-card"
        *ngFor="let talent of talents; let i = index"
        [class.visible]="false"
      >
        <div class="talent-header">
          <div class="talent-images">
            <img
              *ngFor="let image of talent.images; let j = index"
              [src]="image"
              [alt]="talent.title + ' ' + (j + 1)"
              class="talent-image"
              [style.z-index]="talent.images.length - j"
              [style.left.px]="j * 20"
            />
          </div>
          <div class="talent-rating">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
                fill="#fbbf24"
              />
            </svg>
            <span class="rating-value">{{ talent.rating }}</span>
            <span class="rating-label">note moyenne</span>
          </div>
        </div>
        <h3 class="talent-title">{{ talent.title }}</h3>
        <p class="talent-description">{{ talent.description }}</p>
        <a href="#" class="talent-link">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              d="M5 12H19M19 12L12 5M19 12L12 19"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </a>
      </div>
    </div>

    <!-- Lien pour voir plus -->
    <div class="section-footer">
      <a href="#" class="btn btn-outline">Voir plus de compétences</a>
    </div>
  </div>
</section>
