@import "../../../../../assets/scss/abstracts/variables";

// === WHY STYLE EDOMATCH ===
.why-section {
  background: #ffffff;
  padding: 6rem 0;

  @media (max-width: 1024px) {
    padding: 4rem 0;
  }

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    @media (max-width: 768px) {
      margin-bottom: 3rem;
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #0f172a;
      margin-bottom: 1rem;
      letter-spacing: -0.025em;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.125rem;
      color: #64748b;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.7;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

  .reasons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .reason-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.2s ease;

    &:hover {
      border-color: #cbd5e1;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    @media (max-width: 768px) {
      padding: 1.5rem;
    }

    .reason-icon {
      width: 48px;
      height: 48px;
      background: #2563eb;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;

      .material-icons {
        color: white;
        font-size: 1.25rem;
      }
    }

    .reason-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: #0f172a;
      margin-bottom: 0.75rem;
      line-height: 1.3;
    }

    .reason-description {
      font-size: 0.9375rem;
      color: #64748b;
      line-height: 1.6;
    }
  }

  // === CTA SECTION ===
  .why-cta {
    text-align: center;
    margin-top: 4rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem 2rem;

    @media (max-width: 768px) {
      margin-top: 3rem;
      padding: 2rem 1.5rem;
    }

    .cta-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #0f172a;
      margin-bottom: 0.75rem;

      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    .cta-description {
      font-size: 1rem;
      color: #64748b;
      margin-bottom: 2rem;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .cta-buttons {
      display: flex;
      gap: 0.75rem;
      justify-content: center;

      @media (max-width: 640px) {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        padding: 0.875rem 1.75rem;
        font-size: 0.9375rem;
        font-weight: 500;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.2s ease;
        border: 1px solid transparent;

        &.btn-primary {
          background: #0f172a;
          color: white;
          border-color: #0f172a;

          &:hover {
            background: #1e293b;
            border-color: #1e293b;
          }
        }

        &.btn-secondary {
          background: white;
          color: #64748b;
          border-color: #e2e8f0;

          &:hover {
            border-color: #cbd5e1;
            color: #475569;
          }
        }
      }
    }
  }

  // === STATS SECTION ===
  .why-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 4rem;
    padding-top: 3rem;
    border-top: 1px solid #e2e8f0;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
      margin-top: 3rem;
      padding-top: 2rem;
    }

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 1.875rem;
        font-weight: 700;
        color: #0f172a;
        line-height: 1;
        margin-bottom: 0.25rem;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .stat-label {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
      }
    }
  }
}
