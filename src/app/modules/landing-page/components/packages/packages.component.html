<!-- packages.component.html -->
<section class="talent-pricing-section" id="pricing">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Trouvez des talents RH dans votre budget</h2>
      <p class="section-subtitle">
        Explorez les tarifs des professionnels RH sur CompulseHR et trouvez le
        talent parfait pour votre projet.
      </p>
    </div>

    <!-- Navigation des catégories de tarifs -->
    <div class="pricing-tabs">
      <button
        class="tab-button"
        [class.active]="activePricingTab === 'hourly'"
        (click)="setActivePricingTab('hourly')"
      >
        Tarifs horaires
      </button>
      <button
        class="tab-button"
        [class.active]="activePricingTab === 'project'"
        (click)="setActivePricingTab('project')"
      >
        Projets fixes
      </button>
    </div>

    <!-- Contenu des onglets -->
    <div class="pricing-content">
      <!-- Onglet: Tarifs horaires -->
      <div class="tab-panel" [class.active]="activePricingTab === 'hourly'">
        <div class="pricing-grid">
          <div class="pricing-card" *ngFor="let category of hourlyCategories">
            <div class="pricing-header">
              <h3 class="category-title">{{ category.title }}</h3>
              <p class="category-description">{{ category.description }}</p>
            </div>
            <div class="pricing-range">
              <div class="price-range">
                <span class="price-from">{{ category.priceRange.min }}€</span>
                <span class="price-separator">-</span>
                <span class="price-to">{{ category.priceRange.max }}€</span>
                <span class="price-unit">/heure</span>
              </div>
              <div class="price-note">{{ category.priceNote }}</div>
            </div>
            <div class="category-skills">
              <div class="skills-label">Compétences populaires :</div>
              <div class="skills-list">
                <span class="skill-tag" *ngFor="let skill of category.skills">{{
                  skill
                }}</span>
              </div>
            </div>
            <a href="#browse" class="btn btn-outline">Parcourir les talents</a>
          </div>
        </div>
      </div>

      <!-- Onglet: Projets fixes -->
      <div class="tab-panel" [class.active]="activePricingTab === 'project'">
        <div class="pricing-grid">
          <div class="pricing-card" *ngFor="let project of fixedProjects">
            <div class="pricing-header">
              <h3 class="category-title">{{ project.title }}</h3>
              <p class="category-description">{{ project.description }}</p>
            </div>
            <div class="pricing-range">
              <div class="price-range">
                <span class="price-from">{{ project.priceRange.min }}€</span>
                <span class="price-separator">-</span>
                <span class="price-to">{{ project.priceRange.max }}€</span>
                <span class="price-unit">par projet</span>
              </div>
              <div class="price-note">{{ project.priceNote }}</div>
            </div>
            <div class="project-deliverables">
              <div class="deliverables-label">Livrables typiques :</div>
              <ul class="deliverables-list">
                <li *ngFor="let deliverable of project.deliverables">
                  {{ deliverable }}
                </li>
              </ul>
            </div>
            <a href="#post-project" class="btn btn-outline"
              >Publier un projet</a
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Section CTA -->
    <div class="pricing-cta">
      <div class="cta-content">
        <h3 class="cta-title">Prêt à embaucher des talents RH ?</h3>
        <p class="cta-description">
          Rejoignez des milliers d'entreprises qui font confiance à CompulseHR
          pour leurs besoins en ressources humaines.
        </p>
        <div class="cta-buttons">
          <a href="#signup" class="btn btn-primary">Publier un projet</a>
          <a href="#browse" class="btn btn-outline">Parcourir les talents</a>
        </div>
      </div>
      <div class="cta-stats">
        <div class="stat-item">
          <div class="stat-number">95%</div>
          <div class="stat-label">des clients sont satisfaits</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">24h</div>
          <div class="stat-label">pour recevoir des propositions</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">4.9/5</div>
          <div class="stat-label">note moyenne des talents</div>
        </div>
      </div>
    </div>
  </div>
</section>
