import { Component } from '@angular/core';

@Component({
  selector: 'app-packages',
  templateUrl: './packages.component.html',
  styleUrls: ['./packages-edomatch.component.scss'],
})
export class PackagesComponent {
  activePricingTab: string = 'hourly';

  hourlyCategories = [
    {
      title: 'Spécialistes RH Junior',
      description:
        "Professionnels avec 1-3 ans d'expérience en ressources humaines",
      priceRange: { min: 25, max: 45 },
      priceNote: 'Tarif moyen : 35€/heure',
      skills: [
        'Recrutement',
        'Administration RH',
        'Gestion des dossiers',
        'Paie de base',
      ],
    },
    {
      title: 'Consultants RH Expérimentés',
      description:
        "Experts avec 3-7 ans d'expérience en conseil et stratégie RH",
      priceRange: { min: 45, max: 75 },
      priceNote: 'Tarif moyen : 60€/heure',
      skills: [
        'Stratégie RH',
        'Formation',
        'Gestion des talents',
        'Audit RH',
        'SIRH',
      ],
    },
    {
      title: 'Experts RH Senior',
      description: "Consultants senior avec plus de 7 ans d'expérience",
      priceRange: { min: 75, max: 150 },
      priceNote: 'Tarif moyen : 100€/heure',
      skills: [
        'Transformation RH',
        'Direction RH',
        'Change Management',
        "Stratégie d'entreprise",
      ],
    },
  ];

  fixedProjects = [
    {
      title: 'Audit RH Complet',
      description: 'Évaluation complète de vos processus et politiques RH',
      priceRange: { min: 2500, max: 8000 },
      priceNote: "Selon la taille de l'entreprise",
      deliverables: [
        "Rapport d'audit détaillé",
        "Recommandations d'amélioration",
        "Plan d'action prioritaire",
        'Présentation aux dirigeants',
      ],
    },
    {
      title: 'Mise en place SIRH',
      description:
        "Implémentation et configuration d'un système d'information RH",
      priceRange: { min: 5000, max: 25000 },
      priceNote: 'Selon la complexité du système',
      deliverables: [
        'Analyse des besoins',
        'Configuration du système',
        'Formation des utilisateurs',
        'Documentation complète',
      ],
    },
    {
      title: 'Campagne de Recrutement',
      description: "Gestion complète d'une campagne de recrutement spécialisée",
      priceRange: { min: 1500, max: 6000 },
      priceNote: 'Par poste à pourvoir',
      deliverables: [
        'Définition du profil de poste',
        'Sourcing et présélection',
        'Entretiens et évaluations',
        'Rapport de recommandation',
      ],
    },
  ];

  setActivePricingTab(tab: string) {
    this.activePricingTab = tab;
  }
}
