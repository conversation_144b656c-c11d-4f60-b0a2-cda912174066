@import "../../../../../assets/scss/abstracts/variables";

// === SECTION TARIFS TALENTS STYLE UPWORK ===
.talent-pricing-section {
  background: white;
  padding: 4rem 0;

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === HEADER DE SECTION ===
  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.125rem;
      color: #6b7280;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

    .section-subtitle {
      font-size: 1.125rem;
      color: #6b7280;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

  // === ONGLETS DE TARIFICATION ===
  .pricing-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    border-bottom: 1px solid #e5e7eb;

    .tab-button {
      background: none;
      border: none;
      padding: 1rem 2rem;
      font-size: 1rem;
      font-weight: 500;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        color: #374151;
      }

      &.active {
        color: #2563eb;
        border-bottom-color: #2563eb;
      }

      @media (max-width: 768px) {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
      }
    }
  }

  // === CONTENU DES ONGLETS ===
  .pricing-content {
    .tab-panel {
      display: none;

      &.active {
        display: block;
      }
    }
  }

  // === GRILLE DES TARIFS ===
  .pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  // === CARTES DE TARIFICATION STYLE UPWORK ===
  .pricing-card {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 2rem;
    transition: all 0.2s ease;

    &:hover {
      border-color: #d1d5db;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    @media (max-width: 768px) {
      padding: 1.5rem;
    }

    .pricing-header {
      margin-bottom: 1.5rem;

      .category-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.5rem;
        line-height: 1.3;
      }

      .category-description {
        font-size: 0.875rem;
        color: #6b7280;
        line-height: 1.5;
      }
    }

    .pricing-range {
      margin-bottom: 1.5rem;

      .price-range {
        display: flex;
        align-items: baseline;
        gap: 0.25rem;
        margin-bottom: 0.5rem;

        .price-from, .price-to {
          font-size: 1.5rem;
          font-weight: 700;
          color: #111827;
        }

        .price-separator {
          font-size: 1.25rem;
          color: #6b7280;
          margin: 0 0.25rem;
        }

        .price-unit {
          font-size: 0.875rem;
          color: #6b7280;
          margin-left: 0.25rem;
        }
      }

      .price-note {
        font-size: 0.75rem;
        color: #9ca3af;
      }
    }

    .category-skills {
      margin-bottom: 1.5rem;

      .skills-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.75rem;
      }

      .skills-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;

        .skill-tag {
          background: #f3f4f6;
          color: #374151;
          padding: 0.25rem 0.5rem;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 500;
        }
      }
    }

    .project-deliverables {
      margin-bottom: 1.5rem;

      .deliverables-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.75rem;
      }

      .deliverables-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          font-size: 0.875rem;
          color: #6b7280;
          padding: 0.25rem 0;
          position: relative;
          padding-left: 1rem;

          &::before {
            content: '•';
            color: #2563eb;
            position: absolute;
            left: 0;
          }
        }
      }
    }

    .btn {
      width: 100%;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: 6px;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 1px solid transparent;
      text-align: center;
      display: block;

      &.btn-outline {
        background: white;
        color: #374151;
        border-color: #d1d5db;

        &:hover {
          border-color: #9ca3af;
          color: #111827;
        }
      }
    }
  }

  // === SECTION CTA ===
  .pricing-cta {
    background: #f9fafb;
    border-radius: 12px;
    padding: 3rem;
    margin-top: 3rem;

    @media (max-width: 768px) {
      padding: 2rem 1.5rem;
    }

    .cta-content {
      text-align: center;
      margin-bottom: 2rem;

      .cta-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.75rem;
        line-height: 1.3;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .cta-description {
        font-size: 1rem;
        color: #6b7280;
        margin-bottom: 2rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
      }

      .cta-buttons {
        display: flex;
        gap: 0.75rem;
        justify-content: center;

        @media (max-width: 640px) {
          flex-direction: column;
          align-items: center;
        }

        .btn {
          padding: 0.75rem 1.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          border-radius: 6px;
          text-decoration: none;
          transition: all 0.2s ease;
          border: 1px solid transparent;

          &.btn-primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;

            &:hover {
              background: #1d4ed8;
              border-color: #1d4ed8;
            }
          }

          &.btn-outline {
            background: white;
            color: #374151;
            border-color: #d1d5db;

            &:hover {
              border-color: #9ca3af;
              color: #111827;
            }
          }
        }
      }
    }

    .cta-stats {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      text-align: center;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .stat-item {
        .stat-number {
          font-size: 1.5rem;
          font-weight: 700;
          color: #111827;
          margin-bottom: 0.25rem;
          line-height: 1;

          @media (max-width: 768px) {
            font-size: 1.25rem;
          }
        }

        .stat-label {
          font-size: 0.875rem;
          color: #6b7280;
        }
      }
    }
  }
}
