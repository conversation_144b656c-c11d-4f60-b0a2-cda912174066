// === SECTION CATÉGORIES DE TALENTS STYLE UPWORK ===
.talent-categories-section {
  background: white;
  padding: 3rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === NAVIGATION DES CATÉGORIES ===
  .categories-nav {
    margin-bottom: 3rem;

    .categories-list {
      display: flex;
      align-items: center;
      gap: 0;
      list-style: none;
      margin: 0;
      padding: 0;
      border-bottom: 1px solid #e5e7eb;
      overflow-x: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;

      &::-webkit-scrollbar {
        display: none;
      }

      @media (max-width: 768px) {
        gap: 0.5rem;
        padding-bottom: 0.5rem;
      }
    }

    .category-item {
      position: relative;
      white-space: nowrap;

      &.active {
        .category-link {
          color: #2563eb;
          border-bottom-color: #2563eb;
        }
      }

      &.more-dropdown {
        position: relative;

        &:hover .more-dropdown-menu {
          opacity: 1;
          visibility: visible;
        }
      }
    }

    .category-link {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      color: #374151;
      text-decoration: none;
      padding: 1rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        color: #111827;
      }

      @media (max-width: 768px) {
        padding: 0.75rem 1rem;
        font-size: 0.8125rem;
      }

      svg {
        transition: transform 0.2s ease;
      }
    }

    .more-dropdown-menu {
      position: absolute;
      top: 100%;
      left: 0;
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      z-index: 1000;
      min-width: 200px;
      padding: 0.5rem 0;

      .dropdown-item {
        display: block;
        color: #374151;
        text-decoration: none;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        transition: all 0.2s ease;

        &:hover {
          background: #f9fafb;
          color: #111827;
        }

        &:last-child {
          border-top: 1px solid #e5e7eb;
          margin-top: 0.5rem;
          padding-top: 0.75rem;
          font-weight: 500;
          color: #2563eb;

          &:hover {
            background: #eff6ff;
          }
        }
      }
    }
  }

  // === HEADER DE SECTION ===
  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #111827;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.125rem;
      color: #6b7280;
      margin-bottom: 2rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }

    .btn {
      display: inline-flex;
      align-items: center;
      padding: 0.75rem 1.5rem;
      font-size: 1rem;
      font-weight: 500;
      border-radius: 6px;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &.btn-primary {
        background: #2563eb;
        color: white;
        border-color: #2563eb;

        &:hover {
          background: #1d4ed8;
          border-color: #1d4ed8;
        }
      }
    }
  }

  // === IMAGE HERO ===
  .hero-image-container {
    text-align: center;

    .hero-image {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
  }
}
