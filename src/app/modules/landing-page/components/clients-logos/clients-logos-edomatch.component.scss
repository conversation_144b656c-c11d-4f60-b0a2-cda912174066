@import "../../../../../assets/scss/abstracts/variables";

// === CLIENTS LOGOS STYLE EDOMATCH ===
.clients-section {
  background: #f8fafc;
  padding: 4rem 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  .clients-header {
    text-align: center;
    margin-bottom: 3rem;

    @media (max-width: 768px) {
      margin-bottom: 2rem;
    }

    .clients-title {
      font-size: 0.875rem;
      font-weight: 500;
      color: #64748b;
      text-transform: uppercase;
      letter-spacing: 0.05em;
      margin-bottom: 0.5rem;
    }

    .clients-subtitle {
      font-size: 1.125rem;
      font-weight: 600;
      color: #0f172a;
      line-height: 1.5;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

  .clients-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 2rem;
    align-items: center;
    justify-items: center;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
    }

    .client-logo {
      height: 40px;
      width: auto;
      max-width: 120px;
      opacity: 0.6;
      transition: all 0.2s ease;
      filter: grayscale(100%);

      &:hover {
        opacity: 1;
        filter: grayscale(0%);
        transform: scale(1.05);
      }

      @media (max-width: 768px) {
        height: 32px;
        max-width: 100px;
      }
    }
  }

  // === STATS SECTION ===
  .clients-stats {
    display: flex;
    justify-content: center;
    gap: 4rem;
    margin-top: 3rem;
    padding-top: 3rem;
    border-top: 1px solid #e2e8f0;

    @media (max-width: 768px) {
      gap: 2rem;
      margin-top: 2rem;
      padding-top: 2rem;
    }

    @media (max-width: 640px) {
      flex-direction: column;
      gap: 1.5rem;
      text-align: center;
    }

    .stat-item {
      text-align: center;

      .stat-number {
        font-size: 1.875rem;
        font-weight: 700;
        color: #0f172a;
        line-height: 1;
        margin-bottom: 0.25rem;

        @media (max-width: 768px) {
          font-size: 1.5rem;
        }
      }

      .stat-label {
        font-size: 0.875rem;
        color: #64748b;
        font-weight: 500;
      }
    }
  }
}
