import { Component, ElementRef, AfterViewInit, Renderer2 } from '@angular/core';

@Component({
  selector: 'app-how-it-works',
  templateUrl: './how-it-works.component.html',
  styleUrls: ['./how-it-works-edomatch.component.scss'],
})
export class HowItWorksComponent implements AfterViewInit {
  activeTab: string = 'post-job';

  postJobSteps = [
    {
      title: 'Décrivez votre projet',
      description:
        'Publiez un projet et décrivez le travail que vous devez faire. Incluez vos compétences requises, votre budget et votre calendrier.',
      image: 'assets/images/steps/describe-project.png',
    },
    {
      title: 'Recevez des propositions',
      description:
        'Recevez des propositions de talents qualifiés dans les 24 heures. Examinez leurs profils, portfolios et propositions.',
      image: 'assets/images/steps/receive-proposals.png',
    },
    {
      title: 'Embauchez le bon talent',
      description:
        'Interviewez vos favoris et embauchez le meilleur candidat. Les paiements sont sécurisés via notre plateforme.',
      image: 'assets/images/steps/hire-talent.png',
    },
  ];

  browseTalentSteps = [
    {
      title: 'Parcourez les profils',
      description:
        'Explorez notre base de données de talents vérifiés. Filtrez par compétences, expérience, localisation et tarifs.',
      image: 'assets/images/steps/browse-profiles.png',
    },
    {
      title: 'Contactez directement',
      description:
        'Envoyez des messages directs aux talents qui vous intéressent. Discutez de votre projet et négociez les termes.',
      image: 'assets/images/steps/contact-directly.png',
    },
    {
      title: 'Commencez à collaborer',
      description:
        "Une fois l'accord conclu, commencez votre collaboration. Suivez les progrès et gérez les paiements facilement.",
      image: 'assets/images/steps/start-collaboration.png',
    },
  ];

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  setActiveTab(tab: string) {
    this.activeTab = tab;
  }

  ngAfterViewInit() {
    const steps = this.el.nativeElement.querySelectorAll('.step-card');
    const section = this.el.nativeElement.querySelector('.how-to-hire-section');
    if (!steps.length || !section) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            steps.forEach((step: HTMLElement, i: number) => {
              setTimeout(() => {
                this.renderer.addClass(step, 'visible');
              }, i * 200);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.3 }
    );
    observer.observe(section);
  }
}
