<section class="how-to-hire-section" id="how-it-works">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">
        Comment embaucher des talents sur CompulseHR
      </h2>
      <p class="section-subtitle">
        <PERSON><PERSON><PERSON> et embauchez des professionnels qualifiés en quelques étapes
        simples.
      </p>
    </div>

    <!-- Onglets de navigation -->
    <div class="tabs-navigation">
      <button
        class="tab-button"
        [class.active]="activeTab === 'post-job'"
        (click)="setActiveTab('post-job')"
      >
        Publier un projet
      </button>
      <button
        class="tab-button"
        [class.active]="activeTab === 'browse-talent'"
        (click)="setActiveTab('browse-talent')"
      >
        Parcourir les talents
      </button>
    </div>

    <!-- Contenu des onglets -->
    <div class="tab-content">
      <!-- Onglet: Publier un projet -->
      <div class="tab-panel" [class.active]="activeTab === 'post-job'">
        <div class="steps-grid">
          <div
            class="step-card"
            *ngFor="let step of postJobSteps; let i = index"
          >
            <div class="step-number">{{ i + 1 }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
            <div class="step-illustration">
              <img [src]="step.image" [alt]="step.title" class="step-image" />
            </div>
          </div>
        </div>
      </div>

      <!-- Onglet: Parcourir les talents -->
      <div class="tab-panel" [class.active]="activeTab === 'browse-talent'">
        <div class="steps-grid">
          <div
            class="step-card"
            *ngFor="let step of browseTalentSteps; let i = index"
          >
            <div class="step-number">{{ i + 1 }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
            <div class="step-illustration">
              <img [src]="step.image" [alt]="step.title" class="step-image" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Section CTA -->
    <div class="section-cta">
      <h3 class="cta-title">Prêt à commencer ?</h3>
      <p class="cta-description">
        Rejoignez des milliers d'entreprises qui font confiance à CompulseHR
        pour leurs besoins en talents.
      </p>
      <div class="cta-buttons">
        <a href="#signup" class="btn btn-primary">Publier un projet</a>
        <a href="#browse" class="btn btn-outline">Parcourir les talents</a>
      </div>
    </div>
  </div>
</section>
