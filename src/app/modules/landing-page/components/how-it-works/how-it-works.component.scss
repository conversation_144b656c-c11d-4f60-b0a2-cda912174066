@import "../../../../../assets/scss/abstracts/variables";
.howitworks-section {
  background: #fff;
  padding: 100px 0 80px 0;
  .container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 1.5rem;
  }
  .section-header {
    text-align: center;
    margin-bottom: 3.5rem;
    .section-title {
      font-size: 2.5rem;
      font-weight: 800;
      color: #222;
      margin-bottom: 1.2rem;
      font-family: "Inter", "Roboto", <PERSON>l, sans-serif;
    }
    .section-subtitle {
      color: #666;
      font-size: 1.15rem;
      margin-bottom: 0;
    }
  }
  .howitworks-process {
    position: relative;
    margin-bottom: 2.5rem;
    .process-line {
      position: absolute;
      top: 38px;
      left: 0;
      width: 100%;
      height: 60px;
      pointer-events: none;
      z-index: 1;
    }
    .process-path {
      stroke: var(--primary);
      stroke-width: 4;
      fill: none;
      stroke-dasharray: 32 18;
      stroke-dashoffset: 0;
      opacity: 0.22;
      filter: drop-shadow(0 2px 8px var(--primary));
      animation: process-flow-dash 2.2s linear infinite;
      transition: none;
    }
    .howitworks-step.visible ~ .process-line .process-path,
    .howitworks-step.visible + .process-line .process-path {
      stroke-dashoffset: 0;
      opacity: 0.32;
      transition-delay: 0.2s;
    }
  }
  .howitworks-grid {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    z-index: 2;
    gap: 0;
  }
  .howitworks-step {
    position: relative;
    background: #fff;
    border-radius: 1.2rem;
    box-shadow: 0 4px 24px rgba(139, 92, 246, 0.08);
    padding: 2.5rem 1.7rem 2rem 1.7rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    min-width: 260px;
    margin: 0 0.5rem;
    opacity: 0;
    transform: translateY(40px) scale(0.98);
    transition: none;
    .howitworks-icon {
      transition: none;
    }
    &.visible {
      opacity: 1;
      transform: translateY(0) scale(1);
      transition: opacity 0.7s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
      .howitworks-icon {
        animation: icon-pop 0.7s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 0 0 0.2rem var(--primary-light);
      }
    }
    .howitworks-icon {
      margin-bottom: 1.5rem;
      background: #f3f3fa;
      border-radius: 50%;
      width: 56px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 2rem;
      color: var(--primary);
      .material-icons {
        font-size: 2rem;
      }
    }
    .howitworks-title {
      font-size: 1.22rem;
      font-weight: 700;
      color: #222;
      margin-bottom: 0.7rem;
    }
    .howitworks-desc {
      color: #555;
      font-size: 1.05rem;
      margin-bottom: 0;
    }
  }
  .howitworks-step:hover .howitworks-icon {
    color: var(--primary-dark);
  }
}

@media (max-width: 900px) {
  .howitworks-section {
    padding: 60px 0 40px 0;
    .section-title {
      font-size: 1.5rem;
    }
    .howitworks-process .process-line {
      display: none;
    }
    .howitworks-grid {
      flex-direction: column;
      gap: 2.2rem;
    }
    .howitworks-step {
      min-width: 0;
      margin: 0;
    }
    .step-badge {
      top: -22px;
      width: 36px;
      height: 36px;
      font-size: 1rem;
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes icon-pop {
  0% {
    transform: scale(0.7) rotate(-10deg);
    filter: blur(2px);
    opacity: 0.2;
  }
  60% {
    transform: scale(1.15) rotate(6deg);
    filter: blur(0);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(0deg);
    filter: blur(0);
    opacity: 1;
  }
}

@keyframes process-flow-dash {
  0% {
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: -50;
  }
}
