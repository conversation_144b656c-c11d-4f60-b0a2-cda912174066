@import "../../../../../assets/scss/abstracts/variables";

// === COMMENT EMBAUCHER STYLE UPWORK ===
.how-to-hire-section {
  background: white;
  padding: 4rem 0;

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === HEADER DE SECTION ===
  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.125rem;
      color: #6b7280;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

  // === NAVIGATION PAR ONGLETS ===
  .tabs-navigation {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    border-bottom: 1px solid #e5e7eb;

    .tab-button {
      background: none;
      border: none;
      padding: 1rem 2rem;
      font-size: 1rem;
      font-weight: 500;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;

      &:hover {
        color: #374151;
      }

      &.active {
        color: #2563eb;
        border-bottom-color: #2563eb;
      }

      @media (max-width: 768px) {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
      }
    }
  }

  // === CONTENU DES ONGLETS ===
  .tab-content {
    .tab-panel {
      display: none;

      &.active {
        display: block;
      }
    }
  }

  // === GRILLE DES ÉTAPES ===
  .steps-grid {
    display: grid;
    gap: 3rem;

    @media (max-width: 768px) {
      gap: 2rem;
    }

    .step-card {
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: 2rem;
      align-items: center;
      padding: 2rem;
      background: #f9fafb;
      border-radius: 12px;
      transition: all 0.2s ease;
      opacity: 0;
      transform: translateY(20px);

      &:hover {
        background: #f3f4f6;
      }

      &.visible {
        opacity: 1;
        transform: translateY(0);
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
        padding: 1.5rem;
      }

      .step-number {
        width: 48px;
        height: 48px;
        background: #2563eb;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        font-weight: 600;
        flex-shrink: 0;

        @media (max-width: 768px) {
          margin: 0 auto;
        }
      }

      .step-content {
        .step-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: #111827;
          margin-bottom: 0.5rem;
          line-height: 1.3;

          @media (max-width: 768px) {
            font-size: 1.25rem;
          }
        }

        .step-description {
          font-size: 1rem;
          color: #6b7280;
          line-height: 1.6;
        }
      }

      .step-illustration {
        .step-image {
          max-width: 200px;
          height: auto;
          border-radius: 8px;

          @media (max-width: 768px) {
            max-width: 150px;
          }
        }
      }
    }
  }

  // === SECTION CTA ===
  .section-cta {
    text-align: center;
    margin-top: 4rem;
    background: #f9fafb;
    border-radius: 12px;
    padding: 3rem 2rem;

    @media (max-width: 768px) {
      margin-top: 3rem;
      padding: 2rem 1.5rem;
    }

    .cta-title {
      font-size: 1.75rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 0.75rem;
      line-height: 1.3;

      @media (max-width: 768px) {
        font-size: 1.5rem;
      }
    }

    .cta-description {
      font-size: 1rem;
      color: #6b7280;
      margin-bottom: 2rem;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .cta-buttons {
      display: flex;
      gap: 0.75rem;
      justify-content: center;

      @media (max-width: 640px) {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.2s ease;
        border: 1px solid transparent;

        &.btn-primary {
          background: #2563eb;
          color: white;
          border-color: #2563eb;

          &:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
          }
        }

        &.btn-outline {
          background: white;
          color: #374151;
          border-color: #d1d5db;

          &:hover {
            border-color: #9ca3af;
            color: #111827;
          }
        }
      }
    }
  }
}
