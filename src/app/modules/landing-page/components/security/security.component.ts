import { Component, AfterViewInit, ElementRef, Renderer2 } from '@angular/core';

@Component({
  selector: 'app-security',
  templateUrl: './security.component.html',
  styleUrls: ['./security-edomatch.component.scss'],
})
export class SecurityComponent implements AfterViewInit {
  logos = [
    { src: 'assets/images/logo-gdpr.png', alt: 'GDPR' },
    { src: 'assets/images/logo-iso27001.png', alt: 'ISO 27001' },
  ];

  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngAfterViewInit() {
    const logos = this.el.nativeElement.querySelectorAll('.security-logo');
    const section = this.el.nativeElement.querySelector('.security-section');
    if (!logos.length || !section) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            logos.forEach((logo: HTMLElement, i: number) => {
              setTimeout(() => {
                this.renderer.addClass(logo, 'visible');
              }, i * 180);
            });
            observer.disconnect();
          }
        });
      },
      { threshold: 0.2 }
    );
    observer.observe(section);
  }
}
