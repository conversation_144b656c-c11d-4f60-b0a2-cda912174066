.security-section {
  background: #fff;
  padding: 60px 0 50px 0;
  .container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
  }
  .security-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 1.2rem;
    letter-spacing: 0.02em;
  }
  .security-desc {
    color: #555;
    font-size: 1.08rem;
    margin-bottom: 2.2rem;
  }
  .security-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    flex-wrap: wrap;
  }
  .security-logo {
    height: 48px;
    opacity: 0.9;
    filter: grayscale(1);
    transition: opacity 0.2s, filter 0.2s;
    &:hover {
      opacity: 1;
      filter: grayscale(0);
    }
  }
}

@media (max-width: 900px) {
  .security-section {
    padding: 30px 0 20px 0;
    .security-logos {
      gap: 1.2rem;
    }
    .security-logo {
      height: 32px;
    }
  }
}
