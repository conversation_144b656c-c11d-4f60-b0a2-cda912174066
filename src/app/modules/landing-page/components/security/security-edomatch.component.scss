@import "../../../../../assets/scss/abstracts/variables";

// === SECURITY STYLE EDOMATCH ===
.security-section {
  background: #f8fafc;
  padding: 6rem 0;

  @media (max-width: 1024px) {
    padding: 4rem 0;
  }

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  .security-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0f172a;
    text-align: center;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .security-desc {
    font-size: 1.125rem;
    color: #64748b;
    text-align: center;
    max-width: 700px;
    margin: 0 auto 4rem;
    line-height: 1.7;

    @media (max-width: 768px) {
      font-size: 1rem;
      margin-bottom: 3rem;
    }
  }

  .security-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 2rem;
    align-items: center;
    justify-items: center;
    margin-bottom: 4rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.5rem;
      margin-bottom: 3rem;
    }

    .security-logo {
      height: 50px;
      width: auto;
      max-width: 120px;
      opacity: 0.8;
      transition: all 0.2s ease;

      &:hover {
        opacity: 1;
        transform: scale(1.05);
      }

      @media (max-width: 768px) {
        height: 40px;
        max-width: 100px;
      }
    }
  }

  // === SECURITY FEATURES ===
  .security-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .security-feature {
      background: white;
      border: 1px solid #e2e8f0;
      border-radius: 12px;
      padding: 2rem;
      text-align: center;
      transition: all 0.2s ease;

      &:hover {
        border-color: #cbd5e1;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }

      @media (max-width: 768px) {
        padding: 1.5rem;
      }

      .feature-icon {
        width: 48px;
        height: 48px;
        background: #2563eb;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;

        i {
          color: white;
          font-size: 1.25rem;
        }
      }

      .feature-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #0f172a;
        margin-bottom: 0.75rem;
      }

      .feature-description {
        font-size: 0.9375rem;
        color: #64748b;
        line-height: 1.6;
      }
    }
  }

  // === COMPLIANCE SECTION ===
  .compliance-section {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem 2rem;
    margin-top: 4rem;
    text-align: center;

    @media (max-width: 768px) {
      padding: 2rem 1.5rem;
      margin-top: 3rem;
    }

    .compliance-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #0f172a;
      margin-bottom: 1rem;

      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    .compliance-description {
      font-size: 1rem;
      color: #64748b;
      margin-bottom: 2rem;
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .compliance-badges {
      display: flex;
      justify-content: center;
      gap: 2rem;
      flex-wrap: wrap;

      @media (max-width: 768px) {
        gap: 1rem;
      }

      .compliance-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: #f1f5f9;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        color: #475569;

        .badge-icon {
          color: #2563eb;
        }
      }
    }
  }
}
