@import "../../../../../assets/scss/abstracts/variables";

// === INTEGRATIONS STYLE EDOMATCH ===
.integrations-section {
  background: #ffffff;
  padding: 6rem 0;

  @media (max-width: 1024px) {
    padding: 4rem 0;
  }

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  .integrations-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0f172a;
    text-align: center;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .integrations-desc {
    font-size: 1.125rem;
    color: #64748b;
    text-align: center;
    max-width: 600px;
    margin: 0 auto 4rem;
    line-height: 1.7;

    @media (max-width: 768px) {
      font-size: 1rem;
      margin-bottom: 3rem;
    }
  }

  .integrations-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 3rem;
    align-items: center;
    justify-items: center;
    margin-bottom: 4rem;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 2rem;
      margin-bottom: 3rem;
    }

    .integration-logo {
      height: 60px;
      width: auto;
      max-width: 140px;
      opacity: 0.7;
      transition: all 0.2s ease;
      filter: grayscale(100%);

      &:hover {
        opacity: 1;
        filter: grayscale(0%);
        transform: scale(1.05);
      }

      @media (max-width: 768px) {
        height: 48px;
        max-width: 120px;
      }
    }
  }

  // === CTA SECTION ===
  .integrations-cta {
    text-align: center;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem 2rem;

    @media (max-width: 768px) {
      padding: 2rem 1.5rem;
    }

    .cta-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #0f172a;
      margin-bottom: 0.75rem;

      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    .cta-description {
      font-size: 1rem;
      color: #64748b;
      margin-bottom: 2rem;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .cta-buttons {
      display: flex;
      gap: 0.75rem;
      justify-content: center;

      @media (max-width: 640px) {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        padding: 0.875rem 1.75rem;
        font-size: 0.9375rem;
        font-weight: 500;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.2s ease;
        border: 1px solid transparent;

        &.btn-primary {
          background: #0f172a;
          color: white;
          border-color: #0f172a;

          &:hover {
            background: #1e293b;
            border-color: #1e293b;
          }
        }

        &.btn-secondary {
          background: white;
          color: #64748b;
          border-color: #e2e8f0;

          &:hover {
            border-color: #cbd5e1;
            color: #475569;
          }
        }
      }
    }
  }

  // === FEATURES GRID ===
  .integrations-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 4rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
      margin-top: 3rem;
    }

    .feature-item {
      text-align: center;
      padding: 1.5rem;

      .feature-icon {
        width: 48px;
        height: 48px;
        background: #2563eb;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;

        i {
          color: white;
          font-size: 1.25rem;
        }
      }

      .feature-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #0f172a;
        margin-bottom: 0.5rem;
      }

      .feature-description {
        font-size: 0.9375rem;
        color: #64748b;
        line-height: 1.5;
      }
    }
  }
}
