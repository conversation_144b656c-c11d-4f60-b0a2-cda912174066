.integrations-section {
  background: #f3f3fa;
  padding: 60px 0 50px 0;
  .container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 1.5rem;
    text-align: center;
  }
  .integrations-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 1.2rem;
    letter-spacing: 0.02em;
  }
  .integrations-desc {
    color: #555;
    font-size: 1.08rem;
    margin-bottom: 2.2rem;
  }
  .integrations-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    flex-wrap: wrap;
  }
  .integration-logo {
    height: 38px;
    opacity: 0.9;
    filter: grayscale(1);
    transition: opacity 0.2s, filter 0.2s;
    &:hover {
      opacity: 1;
      filter: grayscale(0);
    }
  }
}

@media (max-width: 900px) {
  .integrations-section {
    padding: 30px 0 20px 0;
    .integrations-logos {
      gap: 1.2rem;
    }
    .integration-logo {
      height: 24px;
    }
  }
}
