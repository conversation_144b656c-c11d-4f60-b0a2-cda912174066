@import "../../../../../assets/scss/abstracts/variables";

// === TESTIMONIALS STYLE EDOMATCH ===
.testimonials-section {
  background: #ffffff;
  padding: 6rem 0;

  @media (max-width: 1024px) {
    padding: 4rem 0;
  }

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 2rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;

    @media (max-width: 768px) {
      margin-bottom: 3rem;
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #0f172a;
      margin-bottom: 1rem;
      letter-spacing: -0.025em;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.125rem;
      color: #64748b;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.7;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
  }

  .testimonial-card {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    transition: all 0.2s ease;

    &:hover {
      border-color: #cbd5e1;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    }

    @media (max-width: 768px) {
      padding: 1.5rem;
    }

    .testimonial-content {
      margin-bottom: 1.5rem;

      .testimonial-text {
        font-size: 1rem;
        color: #475569;
        line-height: 1.7;
        font-style: italic;
        position: relative;

        &::before {
          content: '"';
          font-size: 2rem;
          color: #2563eb;
          position: absolute;
          top: -0.5rem;
          left: -0.75rem;
          font-family: serif;
        }

        &::after {
          content: '"';
          font-size: 2rem;
          color: #2563eb;
          font-family: serif;
        }
      }
    }

    .testimonial-author {
      display: flex;
      align-items: center;
      gap: 1rem;

      .author-avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #e2e8f0;
      }

      .author-info {
        .author-name {
          font-size: 0.9375rem;
          font-weight: 600;
          color: #0f172a;
          margin-bottom: 0.25rem;
        }

        .author-title {
          font-size: 0.8125rem;
          color: #64748b;
        }
      }
    }

    .testimonial-rating {
      display: flex;
      gap: 0.25rem;
      margin-bottom: 1rem;

      .star {
        color: #fbbf24;
        font-size: 1rem;
      }
    }
  }

  // === CTA SECTION ===
  .testimonials-cta {
    text-align: center;
    margin-top: 4rem;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 16px;
    padding: 3rem 2rem;

    @media (max-width: 768px) {
      margin-top: 3rem;
      padding: 2rem 1.5rem;
    }

    .cta-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #0f172a;
      margin-bottom: 0.75rem;

      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    .cta-description {
      font-size: 1rem;
      color: #64748b;
      margin-bottom: 2rem;
      max-width: 500px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;
    }

    .cta-buttons {
      display: flex;
      gap: 0.75rem;
      justify-content: center;

      @media (max-width: 640px) {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        padding: 0.875rem 1.75rem;
        font-size: 0.9375rem;
        font-weight: 500;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.2s ease;
        border: 1px solid transparent;

        &.btn-primary {
          background: #0f172a;
          color: white;
          border-color: #0f172a;

          &:hover {
            background: #1e293b;
            border-color: #1e293b;
          }
        }

        &.btn-secondary {
          background: white;
          color: #64748b;
          border-color: #e2e8f0;

          &:hover {
            border-color: #cbd5e1;
            color: #475569;
          }
        }
      }
    }
  }
}
