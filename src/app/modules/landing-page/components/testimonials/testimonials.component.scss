// === SECTION PROFESSIONNELS STYLE UPWORK ===
.professionals-section {
  background: white;
  padding: 4rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === HEADER DE SECTION ===
  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2rem;
      font-weight: 600;
      color: #111827;
      line-height: 1.3;
      max-width: 800px;
      margin: 0 auto;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }
  }

  // === GRILLE DES PROFESSIONNELS ===
  .professionals-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .professional-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 2rem;
      transition: all 0.2s ease;

      &:hover {
        border-color: #d1d5db;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      }

      .professional-quote {
        font-size: 1.125rem;
        color: #374151;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        font-style: italic;

        &::before {
          content: '"';
          color: #2563eb;
          font-size: 1.5rem;
          font-weight: bold;
        }

        &::after {
          content: '"';
          color: #2563eb;
          font-size: 1.5rem;
          font-weight: bold;
        }
      }

      .professional-info {
        display: flex;
        align-items: center;
        gap: 1rem;

        .professional-avatar {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          object-fit: cover;
          flex-shrink: 0;
        }

        .professional-details {
          .professional-name {
            font-size: 1rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 0.25rem;
          }

          .professional-badge {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            font-weight: 500;
            color: #2563eb;
            margin-bottom: 0.25rem;
          }

          .professional-title {
            font-size: 0.875rem;
            color: #6b7280;
          }
        }
      }
    }
  }

.testimonials-carousel-wrapper {
  position: relative;
  overflow: hidden;
  width: 100%;
  padding: 32px 0 36px 0;
  margin: 0 auto;
}
.testimonials-carousel {
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: 0;
  animation: carousel-scroll 24s linear infinite;
  width: 100vw;
  justify-content: center;
}
@keyframes carousel-scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
.testimonial-card {
  width: 90vw;
  max-width: 900px;
  min-width: 320px;
  height: 320px;
  background: linear-gradient(135deg, #f3f4f6 0%, #e0e7ef 100%);
  border-radius: 36px;
  box-shadow: 0 12px 48px rgba(139, 92, 246, 0.13),
    0 0 0 8px rgba(59, 130, 246, 0.08);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 64px;
  margin: 0 2vw;
  position: relative;
  z-index: 2;
  text-align: left;
  overflow: hidden;
  animation: fadeInUp 1s cubic-bezier(0.4, 0, 0.2, 1);
  transition: box-shadow 0.2s, transform 0.2s;
}
.testimonial-card::before {
  content: "“";
  position: absolute;
  top: 24px;
  left: 32px;
  font-size: 4rem;
  color: var(--primary-light);
  opacity: 0.25;
  z-index: 1;
  pointer-events: none;
}
.testimonial-photo {
  width: 110px;
  height: 110px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 48px;
  flex-shrink: 0;
  background: #fff;
  box-shadow: 0 4px 24px rgba(59, 130, 246, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  transition: transform 0.3s, box-shadow 0.3s;
}
.testimonial-card:hover .testimonial-photo {
  transform: scale(1.08) rotate(-3deg);
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.18);
}
.testimonial-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
  z-index: 2;
}
.testimonial-text {
  font-size: 1.35rem;
  color: #222;
  margin-bottom: 1.3rem;
  font-style: italic;
  font-weight: 600;
  line-height: 1.6;
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 12px rgba(139, 92, 246, 0.06);
}
.testimonial-author {
  font-weight: 800;
  color: var(--primary);
  font-size: 1.18rem;
  margin-bottom: 0.2rem;
  letter-spacing: 0.01em;
}
.testimonial-role {
  color: #888;
  font-size: 1.05rem;
  font-weight: 500;
}
.testimonial-badge {
  position: absolute;
  top: 24px;
  right: 32px;
  background: var(--primary);
  color: #fff;
  font-size: 1rem;
  font-weight: 700;
  border-radius: 999px;
  padding: 0.4rem 1.2rem;
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.08);
  z-index: 3;
  letter-spacing: 0.04em;
}
.carousel-fade {
  position: absolute;
  top: 0;
  width: 120px;
  height: 100%;
  z-index: 3;
  pointer-events: none;
}
.carousel-fade-left {
  left: 0;
  background: linear-gradient(90deg, #fff 70%, rgba(255, 255, 255, 0) 100%);
  filter: blur(2px);
}
.carousel-fade-right {
  right: 0;
  background: linear-gradient(270deg, #fff 70%, rgba(255, 255, 255, 0) 100%);
  filter: blur(2px);
}

@media (max-width: 900px) {
  .testimonials-carousel-wrapper {
    padding: 12px 0 14px 0;
  }
  .testimonial-card {
    width: 98vw;
    min-width: 0;
    max-width: 100vw;
    height: 200px;
    border-radius: 18px;
    padding: 0 12px;
  }
  .testimonial-photo {
    width: 60px;
    height: 60px;
    margin-right: 18px;
  }
  .testimonial-text {
    font-size: 1rem;
    margin-bottom: 0.7rem;
  }
  .testimonial-author {
    font-size: 1rem;
  }
  .testimonial-role {
    font-size: 0.92rem;
  }
  .testimonial-badge {
    top: 10px;
    right: 10px;
    font-size: 0.85rem;
    padding: 0.3rem 0.8rem;
  }
  .carousel-fade {
    width: 40px;
  }
}
@media (max-width: 600px) {
  .testimonials-carousel-wrapper {
    padding: 6px 0 8px 0;
  }
  .testimonial-card {
    width: 98vw;
    min-width: 0;
    max-width: 100vw;
    height: 120px;
    border-radius: 8px;
    padding: 0 2px;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  .testimonial-photo {
    width: 36px;
    height: 36px;
    margin: 0 0 4px 0;
  }
  .testimonial-content {
    align-items: center;
  }
  .testimonial-text {
    font-size: 0.82rem;
    margin-bottom: 0.2rem;
  }
  .testimonial-author {
    font-size: 0.8rem;
  }
  .testimonial-role {
    font-size: 0.7rem;
  }
  .testimonial-badge {
    top: 4px;
    right: 4px;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }
}

// === SECTION CTA ===
.cta-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  background: #f9fafb;
  border-radius: 12px;
  padding: 3rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem;
    text-align: center;
  }

  .cta-content {
    .cta-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #111827;
      line-height: 1.4;
      margin-bottom: 1.5rem;

      @media (max-width: 768px) {
        font-size: 1.25rem;
      }
    }

    .btn {
      display: inline-flex;
      align-items: center;
      padding: 0.75rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      border-radius: 6px;
      text-decoration: none;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &.btn-primary {
        background: #2563eb;
        color: white;
        border-color: #2563eb;

        &:hover {
          background: #1d4ed8;
          border-color: #1d4ed8;
        }
      }
    }
  }

  .cta-illustration {
    text-align: center;

    .cta-image {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
    }
  }
}
