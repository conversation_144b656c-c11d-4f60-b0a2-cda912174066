@import "../../../../../assets/scss/abstracts/variables";

// === FAQ STYLE UPWORK ===
.faq-section {
  background: white;
  padding: 4rem 0;

  @media (max-width: 768px) {
    padding: 3rem 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === HEADER DE SECTION ===
  .section-header {
    text-align: center;
    margin-bottom: 3rem;

    .section-title {
      font-size: 2.5rem;
      font-weight: 600;
      color: #111827;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-subtitle {
      font-size: 1.125rem;
      color: #6b7280;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: 1rem;
      }
    }
  }

  // === CATÉGORIES FAQ ===
  .faq-categories {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    border-bottom: 1px solid #e5e7eb;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;

    &::-webkit-scrollbar {
      display: none;
    }

    @media (max-width: 768px) {
      justify-content: flex-start;
      padding-bottom: 0.5rem;
    }

    .category-button {
      background: none;
      border: none;
      padding: 1rem 1.5rem;
      font-size: 0.875rem;
      font-weight: 500;
      color: #6b7280;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;
      white-space: nowrap;

      &:hover {
        color: #374151;
      }

      &.active {
        color: #2563eb;
        border-bottom-color: #2563eb;
      }

      @media (max-width: 768px) {
        padding: 0.75rem 1rem;
        font-size: 0.8125rem;
      }
    }
  }

  // === CONTENU FAQ ===
  .faq-content {
    .faq-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: 3rem;

      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: 2rem;
      }
    }
  }

  // === LISTE FAQ ===
  .faq-list {
    .faq-item {
      border-bottom: 1px solid #e5e7eb;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        background: none;
        border: none;
        width: 100%;
        padding: 1.5rem 0;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        transition: all 0.2s ease;

        @media (max-width: 768px) {
          padding: 1.25rem 0;
        }

        .question-text {
          font-size: 1.125rem;
          font-weight: 500;
          color: #111827;
          line-height: 1.4;
          flex: 1;
          margin-right: 1rem;

          @media (max-width: 768px) {
            font-size: 1rem;
          }
        }

        .faq-icon {
          color: #6b7280;
          transition: all 0.2s ease;
          flex-shrink: 0;

          &.rotated {
            transform: rotate(180deg);
          }
        }

        &:hover {
          .question-text {
            color: #2563eb;
          }
        }
      }

      .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;

        &.open {
          max-height: 300px;
        }

        .answer-content {
          padding-bottom: 1.5rem;
          font-size: 0.875rem;
          color: #6b7280;
          line-height: 1.6;

          @media (max-width: 768px) {
            padding-bottom: 1.25rem;
          }
        }
      }
    }
  }

  // === SECTION D'AIDE ===
  .help-section {
    @media (max-width: 1024px) {
      margin-top: 2rem;
    }

    .help-card {
      background: #f9fafb;
      border-radius: 8px;
      padding: 2rem;
      margin-bottom: 2rem;

      @media (max-width: 768px) {
        padding: 1.5rem;
      }

      .help-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 0.75rem;
        line-height: 1.3;
      }

      .help-description {
        font-size: 0.875rem;
        color: #6b7280;
        margin-bottom: 1.5rem;
        line-height: 1.5;
      }

      .help-actions {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .btn {
          padding: 0.75rem 1rem;
          font-size: 0.875rem;
          font-weight: 500;
          border-radius: 6px;
          text-decoration: none;
          transition: all 0.2s ease;
          border: 1px solid transparent;
          text-align: center;

          &.btn-primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;

            &:hover {
              background: #1d4ed8;
              border-color: #1d4ed8;
            }
          }

          &.btn-outline {
            background: white;
            color: #374151;
            border-color: #d1d5db;

            &:hover {
              border-color: #9ca3af;
              color: #111827;
            }
          }
        }
      }
    }

    .help-stats {
      display: grid;
      grid-template-columns: 1fr;
      gap: 1rem;

      .stat-item {
        text-align: center;
        padding: 1rem;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 6px;

        .stat-number {
          font-size: 1.25rem;
          font-weight: 700;
          color: #111827;
          margin-bottom: 0.25rem;
          line-height: 1;
        }

        .stat-label {
          font-size: 0.75rem;
          color: #6b7280;
          line-height: 1.4;
        }
      }
    }
  }
}
