@import "../../../../../assets/scss/abstracts/variables";

// === FOOTER STYLE UPWORK ===
.footer-section {
  background: #001e00;
  color: white;
  padding: 3rem 0 1.5rem;

  @media (max-width: 768px) {
    padding: 2rem 0 1rem;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;

    @media (max-width: 768px) {
      padding: 0 1rem;
    }
  }

  // === SECTION PRINCIPALE ===
  .footer-main {
    display: grid;
    grid-template-columns: 1fr 3fr;
    gap: 3rem;
    margin-bottom: 2rem;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    @media (max-width: 768px) {
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }

  // === BRAND SECTION ===
  .footer-brand {
    .footer-logo {
      width: 140px;
      height: auto;
      margin-bottom: 1rem;
      filter: brightness(0) invert(1);

      @media (max-width: 768px) {
        width: 120px;
      }
    }

    .footer-tagline {
      font-size: 0.875rem;
      color: #a3a3a3;
      line-height: 1.5;
      margin-bottom: 1.5rem;
      max-width: 280px;

      @media (max-width: 1024px) {
        max-width: none;
      }
    }

    .social-links {
      display: flex;
      gap: 0.75rem;

      @media (max-width: 1024px) {
        display: none;
      }

      .social-link {
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #a3a3a3;
        text-decoration: none;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: white;
        }

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  // === LIENS FOOTER ===
  .footer-links {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 2rem;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.5rem;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 1rem;
    }

    .footer-column {
      .column-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: white;
        margin-bottom: 1rem;
      }

      .column-links {
        list-style: none;
        margin: 0;
        padding: 0;

        li {
          margin-bottom: 0.5rem;

          a {
            font-size: 0.875rem;
            color: #a3a3a3;
            text-decoration: none;
            transition: color 0.2s ease;

            &:hover {
              color: white;
            }
          }
        }
      }
    }
  }

  // === FOOTER BOTTOM ===
  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .footer-left {
      display: flex;
      align-items: center;
      gap: 2rem;

      @media (max-width: 1024px) {
        flex-direction: column;
        gap: 1rem;
      }

      .footer-copyright {
        font-size: 0.875rem;
        color: #a3a3a3;
      }

      .footer-legal {
        display: flex;
        gap: 1.5rem;

        @media (max-width: 768px) {
          gap: 1rem;
        }

        a {
          font-size: 0.875rem;
          color: #a3a3a3;
          text-decoration: none;
          transition: color 0.2s ease;

          &:hover {
            color: white;
          }
        }
      }
    }

    .footer-right {
      @media (min-width: 1025px) {
        display: none;
      }

      .footer-social-mobile {
        display: flex;
        align-items: center;
        gap: 1rem;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 0.5rem;
        }

        span {
          font-size: 0.875rem;
          color: #a3a3a3;
        }

        .social-links-mobile {
          display: flex;
          gap: 0.75rem;

          .social-link {
            width: 28px;
            height: 28px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #a3a3a3;
            text-decoration: none;
            transition: all 0.2s ease;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              color: white;
            }

            svg {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
  }
}
