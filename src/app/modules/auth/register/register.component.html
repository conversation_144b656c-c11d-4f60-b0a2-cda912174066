<div class="container">
  <div class="card">
    <div class="header">
      <h1>Inscription</h1>
      <p>Créez votre compte pour postuler aux offres d'emploi</p>
    </div>

    <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="form">
      <div class="form-row">
        <mat-form-field appearance="outline">
          <mat-label>Prénom</mat-label>
          <input matInput formControlName="firstName" required />
          <mat-error
            *ngIf="registerForm.get('firstName')?.hasError('required')"
          >
            Le prénom est requis
          </mat-error>
          <mat-error
            *ngIf="registerForm.get('firstName')?.hasError('minlength')"
          >
            Le prénom doit contenir au moins 2 caractères
          </mat-error>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Nom</mat-label>
          <input matInput formControlName="lastName" required />
          <mat-error *ngIf="registerForm.get('lastName')?.hasError('required')">
            Le nom est requis
          </mat-error>
          <mat-error
            *ngIf="registerForm.get('lastName')?.hasError('minlength')"
          >
            Le nom doit contenir au moins 2 caractères
          </mat-error>
        </mat-form-field>
      </div>

      <mat-form-field appearance="outline">
        <mat-label>Email</mat-label>
        <input matInput formControlName="email" type="email" required />
        <mat-error *ngIf="registerForm.get('email')?.hasError('required')">
          L'email est requis
        </mat-error>
        <mat-error *ngIf="registerForm.get('email')?.hasError('email')">
          Format d'email invalide
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Téléphone</mat-label>
        <input matInput formControlName="phone" type="tel" />
        <mat-error *ngIf="registerForm.get('phone')?.hasError('pattern')">
          Format de téléphone invalide (10 chiffres)
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Mot de passe</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="password"
          required
        />
        <button
          mat-icon-button
          matSuffix
          (click)="hidePassword = !hidePassword"
          type="button"
        >
          <mat-icon>{{
            hidePassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error>{{ getPasswordErrorMessage() }}</mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline">
        <mat-label>Confirmer le mot de passe</mat-label>
        <input
          matInput
          [type]="hideConfirmPassword ? 'password' : 'text'"
          formControlName="confirmPassword"
          required
        />
        <button
          mat-icon-button
          matSuffix
          (click)="hideConfirmPassword = !hideConfirmPassword"
          type="button"
        >
          <mat-icon>{{
            hideConfirmPassword ? "visibility_off" : "visibility"
          }}</mat-icon>
        </button>
        <mat-error
          *ngIf="registerForm.get('confirmPassword')?.hasError('required')"
        >
          La confirmation du mot de passe est requise
        </mat-error>
        <mat-error
          *ngIf="
            registerForm.get('confirmPassword')?.hasError('passwordMismatch')
          "
        >
          Les mots de passe ne correspondent pas
        </mat-error>
      </mat-form-field>

      <button
        mat-raised-button
        color="primary"
        type="submit"
        [disabled]="isLoading"
        class="submit-button"
      >
        <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
        <span *ngIf="!isLoading">S'inscrire</span>
      </button>

      <div class="links">
        <p>Déjà inscrit ? <a routerLink="/auth/login">Se connecter</a></p>
      </div>
    </form>
  </div>
</div>
