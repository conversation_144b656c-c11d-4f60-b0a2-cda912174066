// Variables
$primary-color: #1976d2;
$primary-light: #42a5f5;
$primary-dark: #1565c0;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: #212529;
$transition: all 0.3s ease;

// Container
.container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(135deg, $primary-light 0%, $primary-dark 100%);
}

// Card
.card {
  width: 100%;
  max-width: 600px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

// Header
.header {
  text-align: center;
  margin-bottom: 2rem;

  h1 {
    color: $gray-900;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: $gray-600;
    font-size: 1rem;
  }
}

// Form
.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    @media (max-width: 600px) {
      grid-template-columns: 1fr;
    }
  }
}

// Submit Button
.submit-button {
  margin-top: 1rem;
  padding: 0.75rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: $transition;

  &:hover:not(:disabled) {
    background-color: $primary-dark;
  }

  &:disabled {
    opacity: 0.7;
  }

  mat-spinner {
    margin-right: 0.5rem;
  }
}

// Links
.links {
  text-align: center;
  margin-top: 1rem;

  p {
    color: $gray-600;
    font-size: 0.9rem;

    a {
      color: $primary-color;
      text-decoration: none;
      font-weight: 500;
      transition: $transition;

      &:hover {
        color: $primary-dark;
        text-decoration: underline;
      }
    }
  }
}

// Material Overrides
::ng-deep {
  .mat-form-field {
    width: 100%;
  }

  .mat-form-field-appearance-outline {
    .mat-form-field-outline {
      color: $gray-300;
    }

    &:hover .mat-form-field-outline {
      color: $gray-400;
    }

    &.mat-focused .mat-form-field-outline {
      color: $primary-color;
    }
  }

  .mat-form-field-label {
    color: $gray-600;
  }

  .mat-form-field-required-marker {
    color: $primary-color;
  }

  .mat-error {
    color: #f44336;
  }
}
