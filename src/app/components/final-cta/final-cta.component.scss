/* === SECTION CTA FINALE - STYLE SEAMLESSHR (ÉPURÉ ET PROFESSIONNEL) === */
.seamless-final-cta {
  padding: 100px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: var(--white);
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;

  .cta-title {
    font-size: 3rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 24px;
    line-height: 1.2;
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: 2.5rem;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .cta-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 40px;

    @media (max-width: 768px) {
      font-size: 1.125rem;
      line-height: 1.5;
      margin-bottom: 32px;
    }
  }

  .cta-actions {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .btn-primary {
      display: inline-block;
      background: var(--white);
      color: #667eea;
      padding: 16px 32px;
      font-size: 1.125rem;
      font-weight: 600;
      border-radius: 12px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
      }

      @media (max-width: 768px) {
        padding: 14px 28px;
        font-size: 1rem;
      }
    }

    .btn-secondary {
      display: inline-block;
      background: transparent;
      color: var(--white);
      border: 2px solid var(--white);
      padding: 14px 30px;
      font-size: 1.125rem;
      font-weight: 600;
      border-radius: 12px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: var(--white);
        color: #667eea;
        transform: translateY(-2px);
      }

      @media (max-width: 768px) {
        padding: 12px 26px;
        font-size: 1rem;
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .final-cta-section {
    padding: 80px 0;
  }
}

@media (max-width: 480px) {
  .final-cta-section {
    padding: 60px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
