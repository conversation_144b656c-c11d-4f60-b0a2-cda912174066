/* === FOOTER - STYLE SEAMLESSHR === */
.footer {
  background: var(--gray-900);
  color: var(--white);
}

/* === SECTION PRINCIPALE DU FOOTER === */
.footer-main {
  padding: 80px 0 60px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: 60px;

  @media (max-width: 1024px) {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 40px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
}

/* === COLONNE BRAND === */
.footer-brand {
  .footer-logo {
    height: 40px;
    width: auto;
    margin-bottom: 24px;
  }

  .footer-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--gray-300);
    margin-bottom: 32px;
    max-width: 400px;

    @media (max-width: 768px) {
      margin: 0 auto 32px;
    }
  }
}

/* === NEWSLETTER === */
.newsletter {
  margin-bottom: 40px;

  h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--white);
    margin-bottom: 16px;
  }

  .newsletter-form {
    display: flex;
    gap: 8px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }

    .newsletter-input {
      flex: 1;
      padding: 12px 16px;
      border: 1px solid var(--gray-600);
      border-radius: 8px;
      background: var(--gray-800);
      color: var(--white);
      font-size: 1rem;

      &::placeholder {
        color: var(--gray-400);
      }

      &:focus {
        outline: none;
        border-color: #3b82f6;
      }

      @media (max-width: 768px) {
        width: 100%;
        max-width: 300px;
      }
    }

    .newsletter-btn {
      padding: 12px 24px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      color: var(--white);
      border: none;
      border-radius: 8px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }
    }
  }
}

/* === RÉSEAUX SOCIAUX === */
.social-links {
  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-400);
    margin-bottom: 16px;
    letter-spacing: 0.1em;
  }

  .social-icons {
    display: flex;
    gap: 16px;

    @media (max-width: 768px) {
      justify-content: center;
    }

    .social-link {
      width: 40px;
      height: 40px;
      background: var(--gray-800);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--gray-400);
      transition: all 0.3s ease;

      &:hover {
        background: #3b82f6;
        color: var(--white);
        transform: translateY(-2px);
      }

      svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}

/* === COLONNES DE LIENS === */
.footer-column {
  .footer-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--white);
    margin-bottom: 24px;
  }

  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 12px;

      a {
        color: var(--gray-300);
        text-decoration: none;
        font-size: 1rem;
        transition: all 0.3s ease;

        &:hover {
          color: var(--white);
          text-decoration: underline;
        }
      }
    }
  }
}

/* === CERTIFICATION === */
.footer-certification {
  margin-top: 32px;

  .cert-badge {
    height: 60px;
    width: auto;
    opacity: 0.8;
    transition: all 0.3s ease;

    &:hover {
      opacity: 1;
    }
  }
}

/* === SECTION DU BAS === */
.footer-bottom {
  background: var(--gray-950);
  padding: 40px 0;
  border-top: 1px solid var(--gray-800);
}

.footer-bottom-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: start;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }
}

/* === INFORMATIONS DE CONTACT === */
.contact-info {
  display: flex;
  gap: 60px;

  @media (max-width: 1024px) {
    justify-content: center;
    flex-wrap: wrap;
    gap: 40px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 32px;
  }

  .office {
    h4 {
      font-size: 1.125rem;
      font-weight: 700;
      color: var(--white);
      margin-bottom: 12px;
    }

    p {
      color: var(--gray-300);
      font-size: 0.875rem;
      line-height: 1.5;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .global-presence {
    h4 {
      font-size: 1.125rem;
      font-weight: 700;
      color: var(--white);
      margin-bottom: 12px;
    }

    .countries {
      display: flex;
      gap: 12px;

      @media (max-width: 768px) {
        justify-content: center;
      }

      .flag {
        width: 32px;
        height: 24px;
        border-radius: 4px;
        opacity: 0.8;
        transition: all 0.3s ease;

        &:hover {
          opacity: 1;
          transform: scale(1.1);
        }
      }
    }
  }
}

/* === SECTION LÉGALE === */
.footer-legal {
  display: flex;
  flex-direction: column;
  gap: 20px;

  @media (max-width: 1024px) {
    align-items: center;
  }

  .copyright {
    p {
      color: var(--gray-400);
      font-size: 0.875rem;
      margin: 0;
    }
  }

  .legal-links {
    display: flex;
    gap: 24px;

    @media (max-width: 768px) {
      flex-wrap: wrap;
      justify-content: center;
      gap: 16px;
    }

    a {
      color: var(--gray-400);
      text-decoration: none;
      font-size: 0.875rem;
      transition: all 0.3s ease;

      &:hover {
        color: var(--white);
        text-decoration: underline;
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .footer-main {
    padding: 60px 0 40px;
  }

  .footer-bottom {
    padding: 32px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
