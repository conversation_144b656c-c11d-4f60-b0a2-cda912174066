/* === SECTION CTA === */
.cta-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.cta-content {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;

  .cta-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    padding: 12px 20px;
    border-radius: 24px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);

    svg {
      color: #fbbf24;
    }
  }

  .cta-title {
    font-family: "DM Sans", "Cal Sans", sans-serif;
    font-size: 3.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 24px;
    line-height: 1.2;
    letter-spacing: -0.01em;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }

    @media (max-width: 480px) {
      font-size: 2rem;
    }
  }

  .cta-description {
    font-size: 1.25rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 1.125rem;
      margin-bottom: 32px;
    }
  }

  .cta-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    align-items: center;
    margin-bottom: 48px;

    @media (max-width: 480px) {
      flex-direction: column;
      gap: 12px;
    }

    .btn {
      font-family: "Inter", sans-serif;
      font-weight: 600;
      text-decoration: none;
      border-radius: 8px;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 16px 32px;
      font-size: 16px;

      &.btn-primary {
        color: #3b82f6;
        background: white;

        &:hover {
          background: #f8fafc;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(255, 255, 255, 0.3);
        }
      }

      &.btn-outline {
        color: white;
        background: transparent;
        border: 2px solid rgba(255, 255, 255, 0.3);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          border-color: rgba(255, 255, 255, 0.5);
          transform: translateY(-2px);
        }
      }

      @media (max-width: 480px) {
        width: 100%;
        justify-content: center;
      }
    }
  }

  .cta-features {
    display: flex;
    gap: 32px;
    justify-content: center;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 8px;
      color: rgba(255, 255, 255, 0.9);
      font-size: 14px;
      font-weight: 500;

      svg {
        color: #22c55e;
        flex-shrink: 0;
      }
    }
  }
}
