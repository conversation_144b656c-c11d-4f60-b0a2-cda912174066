<!-- === SECTION CTA === -->
<section class="cta-section">
  <div class="container">
    <div class="cta-content" data-aos="fade-up">
      <div class="cta-badge">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        Prêt à transformer votre RH ?
      </div>
      
      <h2 class="cta-title">
        Rejoignez plus de 500 entreprises qui font confiance à LuminaHR
      </h2>
      
      <p class="cta-description">
        Découvrez comment notre plateforme peut révolutionner votre gestion RH. 
        Essai gratuit de 14 jours, sans engagement.
      </p>
      
      <div class="cta-buttons">
        <a href="#demo" class="btn btn-primary">
          Demander une démo gratuite
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M5 12h14M12 5l7 7-7 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </a>
        <a href="#contact" class="btn btn-outline">
          Parler à un expert
        </a>
      </div>
      
      <div class="cta-features">
        <div class="feature-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          </svg>
          14 jours d'essai gratuit
        </div>
        <div class="feature-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          </svg>
          Configuration en 24h
        </div>
        <div class="feature-item">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          </svg>
          Support dédié inclus
        </div>
      </div>
    </div>
  </div>
</section>
