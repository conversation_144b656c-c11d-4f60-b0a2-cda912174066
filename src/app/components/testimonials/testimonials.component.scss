/* === SECTION TÉMOIGNAGES === */
.testimonials-section {
  padding: 120px 0;
  background: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 80px;

  .section-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .section-title {
    font-family: "DM Sans", "Cal Sans", sans-serif;
    font-size: 3rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 24px;
    line-height: 1.2;
    letter-spacing: -0.01em;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }

    @media (max-width: 480px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 1.125rem;
    }
  }
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-bottom: 80px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.testimonial-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;

  &.featured {
    border: 2px solid #3b82f6;
    transform: scale(1.02);

    &:hover {
      transform: scale(1.02) translateY(-8px);
    }
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }

  .quote-icon {
    color: #3b82f6;
    margin-bottom: 20px;
  }

  .testimonial-text {
    font-size: 1.125rem;
    line-height: 1.7;
    color: #374151;
    margin-bottom: 24px;
    font-style: italic;
  }

  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 16px;

    .author-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      object-fit: cover;
    }

    .author-info {
      .author-name {
        font-weight: 600;
        color: #0f172a;
        margin-bottom: 4px;
      }

      .author-role {
        color: #64748b;
        font-size: 14px;
        margin-bottom: 2px;
      }

      .company-size {
        color: #3b82f6;
        font-size: 12px;
        font-weight: 500;
      }
    }
  }
}

.trust-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 32px;
  text-align: center;

  @media (max-width: 768px) {
    gap: 24px;
  }

  .stat-item {
    .stat-number {
      font-family: "DM Sans", sans-serif;
      font-size: 2.5rem;
      font-weight: 700;
      color: #3b82f6;
      margin-bottom: 8px;
      display: block;
    }

    .stat-label {
      color: #64748b;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 60px;
  line-height: 1.2;
  letter-spacing: -0.02em;

  @media (max-width: 1024px) {
    font-size: 2.25rem;
  }

  @media (max-width: 768px) {
    font-size: 2rem;
    margin-bottom: 40px;
  }
}

.testimonials-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

.testimonial-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 16px;
  padding: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--gray-300);
  }

  .testimonial-photo {
    margin-bottom: 24px;

    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .testimonial-content {
    p {
      font-size: 1.125rem;
      line-height: 2;
      color: var(--gray-700);
      margin-bottom: 20px;
      font-style: italic;

      @media (max-width: 768px) {
        font-size: 1rem;
        line-height: 1.8;
      }
    }

    .testimonial-name {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--gray-500);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-testimonials {
    padding: 80px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}
