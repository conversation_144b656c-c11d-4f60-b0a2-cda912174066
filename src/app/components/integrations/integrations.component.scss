/* === SECTION INTÉGRATIONS === */
.seamless-integrations {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 20% 80%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    );
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.integrations-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
}

/* === PARTIE TEXTE SEAMLESSHR STYLE === */
.integrations-text {
  .section-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    color: #0f172a;
    margin-bottom: 16px;
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: 2.25rem;
    }

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b82f6;
    margin-bottom: 24px;
    line-height: 1.3;

    @media (max-width: 768px) {
      font-size: 1.25rem;
    }
  }

  .section-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: #64748b;
    margin-bottom: 32px;

    @media (max-width: 768px) {
      font-size: 1rem;
      line-height: 1.5;
    }
  }

  .integration-features {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .feature-item {
      display: flex;
      align-items: center;
      gap: 12px;
      color: #374151;
      font-weight: 500;

      svg {
        color: #3b82f6;
        flex-shrink: 0;
      }

      span {
        font-size: 16px;
      }
    }

    @media (max-width: 768px) {
      align-items: center;
    }
  }
}

/* === LOGOS DES INTÉGRATIONS === */
.integrations-logos {
  .logos-grid {
    position: relative;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    padding: 60px 20px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
      padding: 40px 10px;
    }

    @media (max-width: 480px) {
      grid-template-columns: 1fr;
      gap: 20px;
    }
  }

  .central-hub {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    z-index: 10;

    .hub-icon {
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
      animation: pulse 2s infinite;
    }

    .hub-label {
      font-weight: 600;
      color: #3b82f6;
      font-size: 14px;
      background: white;
      padding: 4px 12px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .logo-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
    padding: 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 100%);
      transform: scaleX(0);
      transform-origin: left;
      transition: transform 0.3s ease;
    }

    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);

      &::before {
        transform: scaleX(1);
      }

      svg {
        color: #3b82f6;
        transform: scale(1.1);
      }
    }

    svg {
      color: #64748b;
      transition: all 0.3s ease;
    }

    span {
      font-weight: 600;
      color: #374151;
      font-size: 14px;
    }

    // Couleurs spécifiques par marque
    &.sap:hover svg {
      color: #0073e6;
    }
    &.oracle:hover svg {
      color: #f80000;
    }
    &.microsoft:hover svg {
      color: #00bcf2;
    }
    &.sage:hover svg {
      color: #00b140;
    }
    &.salesforce:hover svg {
      color: #00a1e0;
    }
    &.google:hover svg {
      color: #4285f4;
    }

    .logo-item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 24px;
      background: var(--white);
      border: 1px solid var(--gray-200);
      border-radius: 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-color: var(--gray-300);
      }

      .integration-logo {
        height: 40px;
        width: auto;
        max-width: 80px;
        opacity: 0.7;
        transition: all 0.3s ease;
        filter: grayscale(100%);

        &:hover {
          opacity: 1;
          filter: grayscale(0%);
        }

        @media (max-width: 768px) {
          height: 32px;
        }
      }
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-integrations {
    padding: 80px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
