import { Component, OnInit } from '@angular/core';
import * as AOS from 'aos';

@Component({
  selector: 'app-products-tabs',
  templateUrl: './products-tabs.component.html',
  styleUrls: ['./products-tabs.component.scss'],
})
export class ProductsTabsComponent implements OnInit {
  activeTab: string = 'hrms';

  ngOnInit(): void {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      offset: 100,
    });
  }

  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }
}
