import {
  Component,
  OnInit,
  On<PERSON><PERSON>roy,
  AfterViewInit,
  ElementRef,
  ViewChild,
} from '@angular/core';
import * as AOS from 'aos';
import { gsap } from 'gsap';

@Component({
  selector: 'app-hero',
  templateUrl: './hero.component.html',
  styleUrls: ['./hero.component.scss'],
})
export class HeroComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('heroSection', { static: false }) heroSection!: ElementRef;

  // Statistiques animées
  stats = [
    { number: 1200, label: 'Entreprises', suffix: '+', current: 0 },
    { number: 150000, label: 'Employés gérés', suffix: '+', current: 0 },
    { number: 40, label: 'Gain productivité', suffix: '%', current: 0 },
  ];

  // Timeline GSAP pour les animations
  private tl: gsap.core.Timeline | null = null;

  ngOnInit(): void {
    // Configuration AOS pour les animations de base
    AOS.init({
      duration: 1000,
      easing: 'ease-out-cubic',
      once: true,
      offset: 50,
      delay: 100,
    });
  }

  ngAfterViewInit(): void {
    // Initialisation des animations GSAP avancées
    this.initAdvancedAnimations();

    // Animation des statistiques avec compteur
    this.animateStats();
  }

  ngOnDestroy(): void {
    // Nettoyage des animations
    if (this.tl) {
      this.tl.kill();
    }
  }

  private initAdvancedAnimations(): void {
    // Timeline principale pour les animations séquentielles
    this.tl = gsap.timeline();

    // Animation des orbs de fond
    gsap.to('.orb-1', {
      duration: 8,
      rotation: 360,
      repeat: -1,
      ease: 'none',
    });

    gsap.to('.orb-2', {
      duration: 12,
      rotation: -360,
      repeat: -1,
      ease: 'none',
    });

    gsap.to('.orb-3', {
      duration: 10,
      rotation: 360,
      repeat: -1,
      ease: 'none',
    });

    // Animation des formes flottantes
    gsap.to('.shape', {
      duration: 20,
      rotation: 360,
      repeat: -1,
      ease: 'none',
      stagger: 2,
    });

    // Animation des particules avec scintillement
    gsap.to('.particle', {
      duration: 3,
      opacity: 1,
      scale: 1.5,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut',
      stagger: 0.5,
    });
  }

  private animateStats(): void {
    // Animation des compteurs de statistiques
    this.stats.forEach((stat, index) => {
      gsap.to(stat, {
        current: stat.number,
        duration: 2,
        delay: 1 + index * 0.3,
        ease: 'power2.out',
        onUpdate: () => {
          // Mise à jour en temps réel du compteur
          stat.current = Math.round(stat.current);
        },
      });
    });
  }

  // Méthode pour formater les nombres avec animation
  formatNumber(value: number): string {
    if (value >= 1000) {
      return (value / 1000).toFixed(value >= 100000 ? 0 : 1) + 'K';
    }
    return value.toString();
  }

  // Gestion des clics sur les CTA
  onDemoClick(): void {
    // Animation de feedback
    gsap.to('.btn-primary', {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut',
    });

    // Ici vous pouvez ajouter la logique pour ouvrir la démo
    console.log('Démonstration demandée');
  }

  onTrialClick(): void {
    // Animation de feedback
    gsap.to('.btn-outline', {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut',
    });

    // Ici vous pouvez ajouter la logique pour l'essai gratuit
    console.log('Essai gratuit demandé');
  }

  // Animation au survol des cartes flottantes
  onCardHover(event: MouseEvent): void {
    const card = event.currentTarget as HTMLElement;
    gsap.to(card, {
      y: -10,
      scale: 1.05,
      duration: 0.3,
      ease: 'power2.out',
    });
  }

  onCardLeave(event: MouseEvent): void {
    const card = event.currentTarget as HTMLElement;
    gsap.to(card, {
      y: 0,
      scale: 1,
      duration: 0.3,
      ease: 'power2.out',
    });
  }
}
