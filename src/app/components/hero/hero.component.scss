/* === HERO SECTION LUMINA HR - DESIGN MODERNE GLASSMORPHISME === */
.lumina-hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(
    135deg,
    #0f172a 0%,
    #1e293b 25%,
    #334155 50%,
    #475569 75%,
    #64748b 100%
  );
  overflow: hidden;
  padding: var(--spacing-20) 0;

  @media (max-width: 768px) {
    min-height: 90vh;
    padding: var(--spacing-16) 0;
  }

  // Background avec effets glassmorphisme
  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;

    // Gradient orbs animés
    .gradient-orbs {
      position: absolute;
      width: 100%;
      height: 100%;

      .orb {
        position: absolute;
        border-radius: 50%;
        filter: blur(40px);
        opacity: 0.6;
        animation: float 8s ease-in-out infinite;

        &.orb-1 {
          width: 300px;
          height: 300px;
          background: radial-gradient(
            circle,
            var(--seamless-blue) 0%,
            transparent 70%
          );
          top: 10%;
          left: 10%;
          animation-delay: 0s;
        }

        &.orb-2 {
          width: 400px;
          height: 400px;
          background: radial-gradient(
            circle,
            var(--edomatch-purple) 0%,
            transparent 70%
          );
          top: 50%;
          right: 10%;
          animation-delay: 2s;
        }

        &.orb-3 {
          width: 250px;
          height: 250px;
          background: radial-gradient(
            circle,
            var(--seamless-orange) 0%,
            transparent 70%
          );
          bottom: 20%;
          left: 50%;
          animation-delay: 4s;
        }
      }
    }

    // Formes géométriques flottantes
    .floating-shapes {
      position: absolute;
      width: 100%;
      height: 100%;

      .shape {
        position: absolute;
        opacity: 0.1;
        animation: rotate 20s linear infinite;

        &.shape-1 {
          width: 60px;
          height: 60px;
          background: var(--gradient-fusion);
          border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
          top: 20%;
          left: 20%;
          animation-delay: 0s;
        }

        &.shape-2 {
          width: 80px;
          height: 80px;
          background: var(--gradient-seamless);
          border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
          top: 60%;
          right: 25%;
          animation-delay: 5s;
        }

        &.shape-3 {
          width: 40px;
          height: 40px;
          background: var(--gradient-edomatch);
          border-radius: 70% 30% 30% 70% / 60% 40% 60% 40%;
          bottom: 30%;
          left: 30%;
          animation-delay: 10s;
        }

        &.shape-4 {
          width: 100px;
          height: 100px;
          background: linear-gradient(
            45deg,
            var(--seamless-green),
            var(--edomatch-cyan)
          );
          border-radius: 40% 60% 60% 40% / 70% 30% 70% 30%;
          top: 40%;
          right: 40%;
          animation-delay: 15s;
        }
      }
    }

    // Particules lumineuses
    .particles-container {
      position: absolute;
      width: 100%;
      height: 100%;

      .particle {
        position: absolute;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: twinkle 3s ease-in-out infinite;

        &.particle-1 {
          width: 2px;
          height: 2px;
          top: 15%;
          left: 15%;
          animation-delay: 0s;
        }

        &.particle-2 {
          width: 3px;
          height: 3px;
          top: 25%;
          right: 20%;
          animation-delay: 1s;
        }

        &.particle-3 {
          width: 1px;
          height: 1px;
          top: 70%;
          left: 25%;
          animation-delay: 2s;
        }

        &.particle-4 {
          width: 2px;
          height: 2px;
          bottom: 30%;
          right: 30%;
          animation-delay: 0.5s;
        }

        &.particle-5 {
          width: 3px;
          height: 3px;
          top: 50%;
          left: 60%;
          animation-delay: 1.5s;
        }

        &.particle-6 {
          width: 1px;
          height: 1px;
          bottom: 40%;
          left: 80%;
          animation-delay: 2.5s;
        }
      }
    }
  }
  .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-16);
    align-items: center;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-12);
      text-align: center;
    }
  }

  // Contenu textuel
  .hero-content {
    @media (max-width: 1024px) {
      order: 2;
    }
  }

  // Badge innovation
  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    margin-bottom: var(--spacing-6);
    animation: glow 2s ease-in-out infinite alternate;

    .badge-icon {
      font-size: var(--text-lg);
    }

    .badge-text {
      color: var(--white);
      font-size: var(--text-sm);
      font-weight: var(--font-medium);
    }
  }

  // Titre principal LuminaHR
  .hero-title {
    font-size: var(--text-7xl);
    font-weight: var(--font-black);
    line-height: 1.1;
    color: var(--white);
    margin-bottom: var(--spacing-6);
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: var(--text-5xl);
    }

    @media (max-width: 768px) {
      font-size: var(--text-4xl);
    }

    .text-gradient {
      background: var(--gradient-fusion);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  // Sous-titre descriptif
  .hero-subtitle {
    font-size: var(--text-xl);
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
    margin-bottom: var(--spacing-8);

    @media (max-width: 768px) {
      font-size: var(--text-lg);
    }
  }

  // Boutons d'action
  .hero-actions {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-8);

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }

    .btn {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-2);
      padding: var(--spacing-4) var(--spacing-8);
      font-size: var(--text-lg);
      font-weight: var(--font-semibold);
      border-radius: var(--radius-xl);
      text-decoration: none;
      transition: var(--transition-all);
      white-space: nowrap;

      &.btn-primary {
        background: var(--gradient-cta);
        color: var(--white);
        border: none;
        box-shadow: var(--shadow-lg);

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--shadow-xl);
          background: var(--gradient-fusion);
        }
      }

      &.btn-outline {
        background: rgba(255, 255, 255, 0.1);
        color: var(--white);
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          border-color: rgba(255, 255, 255, 0.5);
          transform: translateY(-2px);
        }
      }

      i {
        font-size: var(--text-xl);
      }
    }
  }

  // Statistiques de confiance
  .hero-trust {
    .trust-text {
      color: rgba(255, 255, 255, 0.7);
      font-size: var(--text-sm);
      margin-bottom: var(--spacing-4);
      text-align: center;

      @media (max-width: 1024px) {
        text-align: center;
      }
    }

    .trust-stats {
      display: flex;
      gap: var(--spacing-8);
      justify-content: center;

      @media (max-width: 1024px) {
        justify-content: center;
      }

      @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: center;
      }

      .stat-item {
        text-align: center;

        .stat-number {
          display: block;
          font-size: var(--text-3xl);
          font-weight: var(--font-bold);
          color: var(--white);
          margin-bottom: var(--spacing-1);
        }

        .stat-label {
          font-size: var(--text-sm);
          color: rgba(255, 255, 255, 0.7);
          font-weight: var(--font-medium);
        }
      }
    }
  }

  // Visuel dashboard avec glassmorphisme
  .hero-visual {
    @media (max-width: 1024px) {
      order: 1;
    }

    .dashboard-mockup {
      position: relative;
      max-width: 600px;
      margin: 0 auto;

      .mockup-container {
        position: relative;
        border-radius: var(--radius-3xl);
        overflow: hidden;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);

        .dashboard-image {
          width: 100%;
          height: auto;
          display: block;
        }

        .glassmorphism-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.05) 100%
          );
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: var(--radius-3xl);
        }
      }

      // Éléments flottants avec fonctionnalités
      .floating-elements {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;

        .floating-card {
          position: absolute;
          background: rgba(255, 255, 255, 0.95);
          backdrop-filter: blur(20px);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: var(--radius-xl);
          padding: var(--spacing-4);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          animation: float 4s ease-in-out infinite;
          display: flex;
          align-items: center;
          gap: var(--spacing-3);
          min-width: 180px;

          .card-icon {
            font-size: var(--text-2xl);
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--gradient-fusion);
            border-radius: var(--radius-lg);
          }

          .card-content {
            .card-title {
              display: block;
              font-size: var(--text-sm);
              font-weight: var(--font-semibold);
              color: var(--gray-900);
              margin-bottom: var(--spacing-1);
            }

            .card-subtitle {
              font-size: var(--text-xs);
              color: var(--gray-600);
            }
          }

          &.card-employee {
            top: 15%;
            left: -15%;
            animation-delay: 0s;
          }

          &.card-analytics {
            top: 50%;
            right: -15%;
            animation-delay: 1s;
          }

          &.card-automation {
            bottom: 20%;
            left: 10%;
            animation-delay: 2s;
          }

          @media (max-width: 768px) {
            min-width: 140px;
            padding: var(--spacing-3);

            .card-icon {
              width: 32px;
              height: 32px;
              font-size: var(--text-lg);
            }

            .card-content {
              .card-title {
                font-size: var(--text-xs);
              }

              .card-subtitle {
                font-size: 10px;
              }
            }
          }
        }
      }
    }
  }
}

/* === SECTION SOCIAL PROOF LUMINA HR === */
.lumina-social-proof {
  padding: var(--spacing-20) 0;
  background: linear-gradient(
    135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.95) 100%
  );
  position: relative;

  @media (max-width: 768px) {
    padding: var(--spacing-16) 0;
  }

  .container {
    position: relative;
    z-index: 2;
  }

  // Titre de confiance
  .social-proof-header {
    text-align: center;
    margin-bottom: var(--spacing-16);

    .social-proof-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--white);
      margin-bottom: var(--spacing-4);

      @media (max-width: 768px) {
        font-size: var(--text-3xl);
      }
    }

    .social-proof-subtitle {
      font-size: var(--text-xl);
      color: rgba(255, 255, 255, 0.8);
      max-width: 600px;
      margin: 0 auto;

      @media (max-width: 768px) {
        font-size: var(--text-lg);
      }
    }
  }

  // Logos clients avec effet glassmorphisme
  .clients-showcase {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-16);

    @media (max-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-4);
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-3);
    }

    .client-item {
      .client-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: var(--radius-xl);
        padding: var(--spacing-6);
        text-align: center;
        transition: var(--transition-all);

        &:hover {
          transform: translateY(-4px);
          background: rgba(255, 255, 255, 0.15);
          border-color: rgba(255, 255, 255, 0.3);
        }

        .client-logo {
          width: 60px;
          height: 60px;
          margin: 0 auto var(--spacing-3);
          opacity: 0.8;
          transition: var(--transition-opacity);

          &:hover {
            opacity: 1;
          }
        }

        .client-name {
          font-size: var(--text-sm);
          color: rgba(255, 255, 255, 0.9);
          font-weight: var(--font-medium);
        }
      }
    }
  }

  // Témoignage rapide
  .quick-testimonial {
    .testimonial-content {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-2xl);
      padding: var(--spacing-8);
      max-width: 600px;
      margin: 0 auto;
      text-align: center;

      .testimonial-text {
        font-size: var(--text-lg);
        color: var(--white);
        font-style: italic;
        margin-bottom: var(--spacing-6);
        line-height: 1.6;
      }

      .testimonial-author {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-4);

        .author-avatar {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .author-info {
          text-align: left;

          .author-name {
            display: block;
            font-size: var(--text-base);
            font-weight: var(--font-semibold);
            color: var(--white);
          }

          .author-role {
            font-size: var(--text-sm);
            color: rgba(255, 255, 255, 0.7);
          }
        }
      }
    }
  }
}

/* === ANIMATIONS LUMINA HR === */

// Animation de flottement principal
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Animation de rotation pour les formes
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Animation de scintillement pour les particules
@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

// Animation de lueur pour le badge
@keyframes glow {
  0% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.2);
  }
}

// Animation de pulsation pour les orbs
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
  .lumina-hero {
    .hero-background {
      .gradient-orbs .orb {
        &.orb-1 {
          width: 200px;
          height: 200px;
        }
        &.orb-2 {
          width: 250px;
          height: 250px;
        }
        &.orb-3 {
          width: 150px;
          height: 150px;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .lumina-hero {
    .hero-background {
      .floating-shapes .shape {
        display: none;
      }

      .gradient-orbs .orb {
        filter: blur(20px);

        &.orb-1 {
          width: 150px;
          height: 150px;
        }
        &.orb-2 {
          width: 180px;
          height: 180px;
        }
        &.orb-3 {
          width: 120px;
          height: 120px;
        }
      }
    }
  }

  .lumina-social-proof {
    .quick-testimonial {
      .testimonial-content {
        padding: var(--spacing-6);

        .testimonial-author {
          flex-direction: column;
          text-align: center;

          .author-info {
            text-align: center;
          }
        }
      }
    }
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

// Amélioration des performances pour les animations
* {
  will-change: auto;
}

.lumina-hero {
  .hero-background * {
    will-change: transform, opacity;
  }
}

// Préchargement des fonts pour éviter le FOUT
.hero-title,
.hero-subtitle {
  font-display: swap;
}

// Optimisation pour les écrans haute résolution
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .dashboard-image {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

// Mode sombre optionnel (pour future implémentation)
@media (prefers-color-scheme: dark) {
  .lumina-social-proof {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.95) 0%,
      rgba(15, 23, 42, 0.95) 100%
    );
  }
}
