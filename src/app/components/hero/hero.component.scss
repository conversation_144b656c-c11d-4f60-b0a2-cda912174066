/* === HERO SECTION LUMINA HR - STYLE ÉPURÉ PROFESSIONNEL === */
.hero-section {
  padding: 120px 0 80px;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  min-height: 90vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;

  // Effet de profondeur avec des cercles flottants
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at 20% 30%,
        rgba(59, 130, 246, 0.15) 0%,
        transparent 40%
      ),
      radial-gradient(
        circle at 80% 70%,
        rgba(99, 102, 241, 0.12) 0%,
        transparent 35%
      ),
      radial-gradient(
        circle at 60% 20%,
        rgba(34, 197, 94, 0.1) 0%,
        transparent 30%
      );
    z-index: 1;
  }

  // Éléments décoratifs flottants
  &::after {
    content: "";
    position: absolute;
    top: 10%;
    right: 15%;
    width: 200px;
    height: 200px;
    background: radial-gradient(
      circle,
      rgba(59, 130, 246, 0.15) 0%,
      transparent 70%
    );
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
    z-index: 1;
  }

  @media (max-width: 768px) {
    padding: 80px 0 60px;
    min-height: 80vh;
  }

  .container {
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
    z-index: 2;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  .hero-content {
    text-align: center;
  }

  // Badge épuré
  .hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);

    span {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    }
  }

  // Titre principal percutant
  .hero-title {
    font-family: "Playfair Display", Georgia, serif;
    font-size: 8rem;
    font-weight: 500;
    line-height: 1.1;
    color: #ffffff;
    margin-bottom: 32px;
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: 6rem;
    }

    @media (max-width: 768px) {
      font-size: 4rem;
    }

    @media (max-width: 480px) {
      font-size: 3rem;
    }
  }

  // Sous-titre épuré
  .hero-subtitle {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 24px;
    line-height: 1.6;
    color: #cbd5e1;
    margin-bottom: 48px;

    @media (max-width: 768px) {
      font-size: 20px;
      margin-bottom: 40px;
    }
  }

  // Actions épurées
  .hero-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 60px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
      padding: 16px 32px;
      border: none;
      border-radius: 8px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .btn-secondary {
      background: transparent;
      color: #475569;
      padding: 16px 32px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f8fafc;
        border-color: #cbd5e1;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
  // Indicateurs de confiance épurés
  .hero-trust {
    margin-bottom: 60px;

    .trust-text {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      color: #64748b;
      margin-bottom: 24px;
      text-align: center;
    }

    .trust-indicators {
      display: flex;
      justify-content: center;
      gap: 48px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 24px;
        align-items: center;
      }

      .indicator {
        text-align: center;

        .number {
          display: block;
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 32px;
          font-weight: 700;
          color: #0f172a;
          margin-bottom: 4px;
        }

        .label {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }
  }

  // Visuel épuré
  .hero-visual {
    .dashboard-preview {
      max-width: 600px;
      margin: 0 auto;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;

      .dashboard-image {
        width: 100%;
        height: auto;
        display: block;
      }
    }
  }
}

// Animation pour les éléments flottants
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}
