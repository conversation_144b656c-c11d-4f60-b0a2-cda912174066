/* === HERO SECTION - STYLE SEAMLESSHR AVEC ANIMATIONS EDOMATCH === */
.twenty-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  padding: 120px 0 80px;
  position: relative;
  text-align: center;
  overflow: hidden;
}

/* === BACKGROUND ANIMÉ AVEC PARTICULES === */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.particles-container {
  position: absolute;
  width: 100%;
  height: 100%;

  .particle {
    position: absolute;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-radius: 50%;
    animation: float-particle 20s infinite linear;

    &.particle-1 {
      width: 60px;
      height: 60px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
      animation-duration: 25s;
    }

    &.particle-2 {
      width: 40px;
      height: 40px;
      top: 20%;
      right: 15%;
      animation-delay: 5s;
      animation-duration: 30s;
    }

    &.particle-3 {
      width: 80px;
      height: 80px;
      bottom: 30%;
      left: 5%;
      animation-delay: 10s;
      animation-duration: 35s;
    }

    &.particle-4 {
      width: 50px;
      height: 50px;
      bottom: 20%;
      right: 10%;
      animation-delay: 15s;
      animation-duration: 28s;
    }

    &.particle-5 {
      width: 30px;
      height: 30px;
      top: 50%;
      left: 50%;
      animation-delay: 8s;
      animation-duration: 22s;
    }

    &.particle-6 {
      width: 70px;
      height: 70px;
      top: 70%;
      right: 30%;
      animation-delay: 12s;
      animation-duration: 32s;
    }
  }
}

/* === EFFETS DE BRILLANCE SUBTILS === */
.shine-effects {
  position: absolute;
  width: 100%;
  height: 100%;

  .shine {
    position: absolute;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    border-radius: 50%;
    animation: shine-pulse 8s ease-in-out infinite;

    &.shine-1 {
      width: 200px;
      height: 200px;
      top: 10%;
      right: 20%;
      animation-delay: 0s;
    }

    &.shine-2 {
      width: 150px;
      height: 150px;
      bottom: 20%;
      left: 25%;
      animation-delay: 3s;
    }

    &.shine-3 {
      width: 100px;
      height: 100px;
      top: 60%;
      right: 40%;
      animation-delay: 6s;
    }
  }
}

/* === AVATARS FLOTTANTS COMME EDOMATCH === */
.floating-avatars {
  position: absolute;
  width: 100%;
  height: 100%;

  .avatar {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid var(--white);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    animation: float-avatar 6s ease-in-out infinite;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-status {
      position: absolute;
      bottom: 2px;
      right: 2px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      border: 2px solid var(--white);

      &.online {
        background: #10b981;
      }

      &.away {
        background: #f59e0b;
      }
    }

    &.avatar-1 {
      top: 15%;
      left: 8%;
      animation-delay: 0s;
    }

    &.avatar-2 {
      top: 25%;
      right: 12%;
      animation-delay: 2s;
    }

    &.avatar-3 {
      bottom: 35%;
      left: 15%;
      animation-delay: 4s;
    }

    &.avatar-4 {
      bottom: 25%;
      right: 8%;
      animation-delay: 1s;
    }

    @media (max-width: 768px) {
      width: 50px;
      height: 50px;

      .avatar-status {
        width: 14px;
        height: 14px;
      }
    }
  }
}

/* === BADGE ANIMÉ === */
.hero-badge {
  display: inline-block;
  margin-bottom: 32px;

  .badge-text {
    display: inline-block;
    padding: 8px 16px;
    background: var(--gray-100);
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 20px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
}

/* === TITRE SEAMLESSHR STYLE === */
.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  color: var(--gray-900);
  margin-bottom: 24px;
  letter-spacing: -0.02em;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 1024px) {
    font-size: 3rem;
  }

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
}

/* === SOUS-TITRE SEAMLESSHR STYLE === */
.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--gray-600);
  margin-bottom: 40px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: 32px;
    line-height: 1.5;
  }
}

/* === BOUTON D'ACTION SEAMLESSHR STYLE === */
.hero-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 60px;

  @media (max-width: 768px) {
    margin-bottom: 40px;
  }

  .btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    background: var(--seamless-blue);
    color: var(--white);
    transition: all 0.3s ease;
    border: none;

    &:hover {
      background: var(--seamless-blue-dark);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(26, 115, 232, 0.3);
    }

    @media (max-width: 768px) {
      padding: 14px 28px;
      font-size: 0.9rem;
    }
  }
}

/* === VISUEL AVEC ANIMATIONS === */
.hero-visual {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.dashboard-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);

  .dashboard-image {
    width: 100%;
    height: auto;
    display: block;
  }
}

/* === ÉLÉMENTS FLOTTANTS ANIMÉS === */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  .floating-card {
    position: absolute;
    background: var(--white);
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--gray-200);
    animation: float 6s ease-in-out infinite;

    .card-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .card-label {
        font-size: 0.75rem;
        color: var(--gray-500);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .card-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
      }
    }

    &.card-1 {
      top: 20%;
      right: -10%;
      animation-delay: 0s;
    }

    &.card-2 {
      bottom: 30%;
      left: -15%;
      animation-delay: 2s;
    }

    &.card-3 {
      top: 60%;
      right: 10%;
      animation-delay: 4s;
    }

    @media (max-width: 768px) {
      display: none;
    }
  }
}

/* === ANIMATIONS === */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* === ANIMATIONS PARTICULES === */
@keyframes float-particle {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
  75% {
    opacity: 0.4;
  }
  100% {
    transform: translateY(0px) rotate(360deg);
    opacity: 0.3;
  }
}

/* === ANIMATIONS AVATARS === */
@keyframes float-avatar {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

/* === ANIMATIONS BRILLANCE === */
@keyframes shine-pulse {
  0%,
  100% {
    opacity: 0;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.2);
  }
}

/* === SECTION CLIENTS - STYLE SEAMLESSHR === */
.seamless-clients {
  background: var(--white);
  padding: 80px 0;
  text-align: center;

  .clients-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.3;

    @media (max-width: 768px) {
      font-size: 1.5rem;
      margin-bottom: 40px;
    }
  }

  .clients-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60px;
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;

    @media (max-width: 768px) {
      gap: 40px;
    }

    .client-item {
      display: flex;
      justify-content: center;
      align-items: center;

      .client-logo {
        height: 50px;
        width: auto;
        max-width: 140px;
        opacity: 0.6;
        transition: all 0.3s ease;
        filter: grayscale(100%);

        &:hover {
          opacity: 1;
          filter: grayscale(0%);
          transform: scale(1.05);
        }

        @media (max-width: 768px) {
          height: 40px;
          max-width: 120px;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-hero {
    padding: 120px 0 80px;
  }

  .trusted-companies {
    padding: 60px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
