/* === HERO SECTION LUMINA HR - STYLE ÉPURÉ PROFESSIONNEL === */
.hero-section {
  padding: 100px 0 120px;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a2332 50%, #2a3441 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;

  // Effet de profondeur subtil et épuré
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 30% 40%,
      rgba(34, 197, 94, 0.08) 0%,
      transparent 50%
    );
    z-index: 1;
  }

  @media (max-width: 768px) {
    padding: 80px 0 60px;
    min-height: 80vh;
  }

  .container {
    margin: 0 auto;
    padding: 0 24px;
    position: relative;
    z-index: 2;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  .hero-content {
    text-align: center;
  }

  // Badge épuré
  .hero-badge {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    color: #e2e8f0;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);

    span {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    }
  }

  // Titre principal épuré et moderne
  .hero-title {
    font-family: "DM Sans", "Cal Sans", sans-serif;
    font-size: 4.5rem;
    font-weight: 600;
    line-height: 1.2;
    color: #ffffff;
    margin-bottom: 24px;
    letter-spacing: -0.01em;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 1024px) {
      font-size: 3.5rem;
      max-width: 700px;
    }

    @media (max-width: 768px) {
      font-size: 2.5rem;
      max-width: 500px;
    }

    @media (max-width: 480px) {
      font-size: 2rem;
      max-width: 350px;
    }
  }

  // Sous-titre épuré et lisible
  .hero-subtitle {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 1.25rem;
    font-weight: 400;
    line-height: 1.6;
    color: #94a3b8;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 1.125rem;
      margin-bottom: 32px;
      max-width: 400px;
    }

    @media (max-width: 480px) {
      font-size: 1rem;
      max-width: 300px;
    }
  }

  // Actions épurées
  .hero-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 60px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
      padding: 16px 32px;
      border: none;
      border-radius: 8px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .btn-secondary {
      background: transparent;
      color: #475569;
      padding: 16px 32px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f8fafc;
        border-color: #cbd5e1;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
  // Indicateurs de confiance épurés
  .hero-trust {
    margin-bottom: 60px;

    .trust-text {
      font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      color: #64748b;
      margin-bottom: 24px;
      text-align: center;
    }

    .trust-indicators {
      display: flex;
      justify-content: center;
      gap: 48px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 24px;
        align-items: center;
      }

      .indicator {
        text-align: center;

        .number {
          display: block;
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 32px;
          font-weight: 700;
          color: #22c55e;
          margin-bottom: 4px;
        }

        .label {
          font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }
  }

  // Visuel épuré et plus grand
  .hero-visual {
    margin-top: 80px;
    text-align: center;

    .dashboard-preview {
      position: relative;
      max-width: 1000px;
      margin: 0 auto;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(
          135deg,
          rgba(34, 197, 94, 0.08) 0%,
          rgba(59, 130, 246, 0.05) 100%
        );
        z-index: 1;
      }

      .dashboard-image {
        width: 100%;
        height: auto;
        display: block;
        position: relative;
        z-index: 2;
        transform: scale(1.02);
      }
    }

    @media (max-width: 768px) {
      margin-top: 60px;

      .dashboard-preview {
        max-width: 100%;
        border-radius: 16px;
      }

      .dashboard-image {
        width: 100%;
        height: auto;
        display: block;
        transform: scale(1);
      }
    }
  }
}

// Cluster d'avatars groupés style EdMatch
.avatar-cluster {
  position: absolute;
  bottom: 15%;
  left: 50%;
  transform: translateX(-50%);
  width: 300px;
  height: 200px;
  z-index: 3;
  pointer-events: none;

  .glow-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 120px;
    background: radial-gradient(
      ellipse,
      rgba(34, 197, 94, 0.25) 0%,
      rgba(34, 197, 94, 0.1) 50%,
      transparent 80%
    );
    border-radius: 50%;
    filter: blur(15px);
    animation: pulse 4s ease-in-out infinite;
  }

  .avatar {
    position: absolute;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
    animation: floatGentle 6s ease-in-out infinite;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    &.avatar-center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 80px;
      height: 80px;
      z-index: 5;
      border: 3px solid rgba(255, 255, 255, 0.4);
      animation-delay: 0s;
    }

    &.avatar-top-left {
      top: 20%;
      left: 20%;
      animation-delay: 0.5s;
    }

    &.avatar-top-right {
      top: 20%;
      right: 20%;
      animation-delay: 1s;
    }

    &.avatar-bottom-left {
      bottom: 20%;
      left: 20%;
      animation-delay: 1.5s;
    }

    &.avatar-bottom-right {
      bottom: 20%;
      right: 20%;
      animation-delay: 2s;
    }

    &.avatar-left {
      top: 50%;
      left: 5%;
      transform: translateY(-50%);
      animation-delay: 0.3s;
    }

    &.avatar-right {
      top: 50%;
      right: 5%;
      transform: translateY(-50%);
      animation-delay: 1.3s;
    }

    &.avatar-far-top {
      top: 5%;
      left: 50%;
      transform: translateX(-50%);
      width: 55px;
      height: 55px;
      animation-delay: 1.8s;
    }
  }
}

// Particules épurées et subtiles
.particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;

  .particle {
    position: absolute;
    border-radius: 50%;
    background: rgba(34, 197, 94, 0.2);

    &.particle-1 {
      width: 3px;
      height: 3px;
      top: 15%;
      left: 20%;
      animation: particleFloat 20s linear infinite;
    }

    &.particle-2 {
      width: 2px;
      height: 2px;
      top: 70%;
      left: 80%;
      animation: particleFloat 25s linear infinite 5s;
    }

    &.particle-3 {
      width: 4px;
      height: 4px;
      top: 40%;
      left: 85%;
      animation: particleFloat 18s linear infinite 10s;
    }
  }
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes floatGentle {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-8px) scale(1.02);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.25;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes floatFast {
  0%,
  100% {
    transform: translateY(0px) translateX(0px) scale(1) rotate(0deg);
  }
  25% {
    transform: translateY(-10px) translateX(8px) scale(1.1) rotate(2deg);
  }
  75% {
    transform: translateY(15px) translateX(-8px) scale(0.9) rotate(-2deg);
  }
}

@keyframes particleFloat {
  0% {
    transform: translateY(0px) translateX(0px) opacity(0);
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) translateX(50px) opacity(0);
  }
}
