/* === HERO SECTION LUMINA HR - STYLE ÉPURÉ PROFESSIONNEL === */
.hero-section {
  padding: 120px 0 80px;
  background: #fafbfc;
  min-height: 90vh;
  display: flex;
  align-items: center;

  @media (max-width: 768px) {
    padding: 80px 0 60px;
    min-height: 80vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    @media (max-width: 768px) {
      padding: 0 20px;
    }
  }

  .hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  // Badge épuré
  .hero-badge {
    display: inline-block;
    background: #f1f5f9;
    color: #475569;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 32px;
    border: 1px solid #e2e8f0;

    span {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    }
  }

  // Titre principal épuré style Gusto
  .hero-title {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 64px;
    font-weight: 700;
    line-height: 1.1;
    color: #0f172a;
    margin-bottom: 24px;
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: 48px;
    }

    @media (max-width: 768px) {
      font-size: 36px;
    }

    em {
      font-style: normal;
      color: #3b82f6;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: 2px;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
        border-radius: 2px;
        opacity: 0.3;
      }
    }
  }

  // Sous-titre épuré
  .hero-subtitle {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 20px;
    line-height: 1.6;
    color: #64748b;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 18px;
      margin-bottom: 32px;
    }
  }

  // Actions épurées
  .hero-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-bottom: 60px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
      padding: 16px 32px;
      border: none;
      border-radius: 8px;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .btn-secondary {
      background: transparent;
      color: #475569;
      padding: 16px 32px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f8fafc;
        border-color: #cbd5e1;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
  // Indicateurs de confiance épurés
  .hero-trust {
    margin-bottom: 60px;

    .trust-text {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 16px;
      color: #64748b;
      margin-bottom: 24px;
      text-align: center;
    }

    .trust-indicators {
      display: flex;
      justify-content: center;
      gap: 48px;

      @media (max-width: 768px) {
        flex-direction: column;
        gap: 24px;
        align-items: center;
      }

      .indicator {
        text-align: center;

        .number {
          display: block;
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 32px;
          font-weight: 700;
          color: #0f172a;
          margin-bottom: 4px;
        }

        .label {
          font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
          font-size: 14px;
          color: #64748b;
          font-weight: 500;
        }
      }
    }
  }

  // Visuel épuré
  .hero-visual {
    .dashboard-preview {
      max-width: 600px;
      margin: 0 auto;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;

      .dashboard-image {
        width: 100%;
        height: auto;
        display: block;
      }
    }
  }
}


