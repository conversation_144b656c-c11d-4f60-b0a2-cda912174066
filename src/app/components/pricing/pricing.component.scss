/* === SECTION TARIFICATION === */
.pricing-section {
  padding: 120px 0;
  background: white;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.03) 0%, transparent 50%);
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 60px;

  .section-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .section-title {
    font-family: "DM Sans", "Cal Sans", sans-serif;
    font-size: 3rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 24px;
    line-height: 1.2;
    letter-spacing: -0.01em;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }

    @media (max-width: 480px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 1.125rem;
    }
  }
}

.billing-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 60px;

  .toggle-label {
    font-weight: 500;
    color: #64748b;
    font-size: 16px;
  }

  .discount-badge {
    background: #3b82f6;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 8px;
  }

  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;

    input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #cbd5e1;
      transition: 0.3s;
      border-radius: 34px;

      &::before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.3s;
        border-radius: 50%;
      }
    }

    input:checked + .slider {
      background-color: #3b82f6;
    }

    input:checked + .slider::before {
      transform: translateX(26px);
    }
  }
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 32px;
  margin-bottom: 80px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.pricing-card {
  background: white;
  border-radius: 20px;
  padding: 40px 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }

  &.popular {
    border: 2px solid #3b82f6;
    transform: scale(1.05);

    &:hover {
      transform: scale(1.05) translateY(-8px);
    }

    .popular-badge {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      background: #3b82f6;
      color: white;
      padding: 8px 24px;
      border-radius: 0 0 12px 12px;
      font-size: 14px;
      font-weight: 600;
    }

    .plan-header {
      margin-top: 20px;
    }
  }

  .plan-header {
    text-align: center;
    margin-bottom: 32px;

    .plan-name {
      font-family: "DM Sans", sans-serif;
      font-size: 1.5rem;
      font-weight: 700;
      color: #0f172a;
      margin-bottom: 8px;
    }

    .plan-description {
      color: #64748b;
      margin-bottom: 24px;
      font-size: 14px;
    }

    .plan-price {
      .price-amount {
        font-family: "DM Sans", sans-serif;
        font-size: 3rem;
        font-weight: 700;
        color: #3b82f6;

        &.hidden {
          display: none;
        }
      }

      .price-period {
        color: #64748b;
        font-size: 16px;
        margin-left: 4px;
      }
    }
  }

  .plan-features {
    margin-bottom: 32px;

    .features-list {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        color: #475569;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }

        svg {
          color: #3b82f6;
          flex-shrink: 0;
        }
      }
    }
  }

  .plan-cta {
    .btn {
      width: 100%;
      font-family: "Inter", sans-serif;
      font-weight: 600;
      text-decoration: none;
      border-radius: 8px;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 14px 28px;
      font-size: 16px;

      &.btn-primary {
        color: white;
        background: #3b82f6;

        &:hover {
          background: #2563eb;
          transform: translateY(-2px);
          box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }
      }

      &.btn-outline {
        color: #3b82f6;
        background: transparent;
        border: 2px solid #3b82f6;

        &:hover {
          background: #3b82f6;
          color: white;
          transform: translateY(-2px);
        }
      }
    }
  }
}

.pricing-faq {
  text-align: center;

  h3 {
    font-family: "DM Sans", sans-serif;
    font-size: 2rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 48px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 1.75rem;
      margin-bottom: 32px;
    }
  }

  .faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    text-align: left;

    @media (max-width: 768px) {
      gap: 24px;
    }
  }

  .faq-item {
    background: rgba(59, 130, 246, 0.02);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(59, 130, 246, 0.1);

    h4 {
      font-family: "DM Sans", sans-serif;
      font-size: 1.125rem;
      font-weight: 600;
      color: #0f172a;
      margin-bottom: 12px;
      line-height: 1.3;
    }

    p {
      color: #64748b;
      line-height: 1.6;
      margin: 0;
    }
  }
}
