/* === SECTION SERVICES === */
.services-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 20% 80%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    );
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 80px;

  .section-badge {
    display: inline-block;
    background: rgba(59, 130, 246, 0.1);
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 24px;
    border: 1px solid rgba(59, 130, 246, 0.2);
  }

  .section-title {
    font-family: "DM Sans", "Cal Sans", sans-serif;
    font-size: 3rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 24px;
    line-height: 1.2;
    letter-spacing: -0.01em;

    @media (max-width: 768px) {
      font-size: 2.5rem;
    }

    @media (max-width: 480px) {
      font-size: 2rem;
    }
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: #64748b;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 1.125rem;
    }
  }
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

.service-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);

    &::before {
      transform: scaleX(1);
    }

    .service-icon {
      transform: scale(1.1);
      color: #3b82f6;
    }
  }

  .service-icon {
    width: 64px;
    height: 64px;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #3b82f6;
    margin-bottom: 24px;
    transition: all 0.3s ease;
  }

  .service-title {
    font-family: "DM Sans", sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    color: #0f172a;
    margin-bottom: 16px;
    line-height: 1.3;
  }

  .service-description {
    color: #64748b;
    line-height: 1.6;
    font-size: 1rem;
  }
}
