@import "../../../styles.scss";

.main-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: sticky;
  top: 0;
  z-index: 100;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.2rem 2rem;
}
.logo-area {
  display: flex;
  align-items: center;
}
.logo-img {
  height: 2.2rem;
  width: auto;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
}
.nav-links {
  display: flex;
  gap: 2.2rem;
  list-style: none;
  margin: 0;
  padding: 0;
  align-items: center;
}
.nav-links > li {
  position: relative;
}
.nav-links a {
  text-decoration: none;
  color: $dark;
  font-weight: 500;
  font-size: 1.08rem;
  transition: color 0.2s;
  padding: 0.3rem 0.5rem;
  display: block;
  &:hover {
    color: $primary;
  }
}
.cta-btn {
  background: $primary;
  color: #fff;
  padding: 0.7rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
  margin-left: 1.5rem;
  &:hover {
    background: darken($primary, 7%);
    box-shadow: 0 4px 16px rgba(26, 115, 232, 0.12);
  }
}
.has-dropdown:hover .dropdown,
.has-dropdown:focus-within .dropdown {
  display: block;
}
.has-dropdown > a:after {
  content: " ▼";
  font-size: 0.7em;
  color: $grey;
}
.dropdown {
  display: none;
  position: absolute;
  left: 0;
  top: 2.2rem;
  background: #fff;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  min-width: 180px;
  z-index: 10;
  padding: 0.5rem 0;
}
.dropdown li {
  width: 100%;
}
.dropdown a {
  color: $dark;
  padding: 0.7rem 1.2rem;
  font-size: 1rem;
  border-radius: 6px;
  &:hover {
    background: $light;
    color: $primary;
  }
}
// Responsive
@media (max-width: 900px) {
  .navbar {
    flex-direction: column;
    gap: 1.2rem;
    padding: 1rem 0.5rem;
  }
  .nav-links {
    gap: 1.2rem;
  }
  .cta-btn {
    margin-left: 0;
  }
}
