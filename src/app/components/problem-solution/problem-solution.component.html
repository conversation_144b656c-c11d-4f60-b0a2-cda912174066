<!-- SECTION PROBLEM/SOLUTION LUMINA HR -->
<section class="lumina-problem-solution">
  <div class="container">
    <!-- En-tête de section -->
    <div class="section-header" data-aos="fade-up">
      <h2 class="section-title">
        Les défis RH d'aujourd'hui vs
        <span class="text-gradient">LuminaHR</span>
      </h2>
      <p class="section-subtitle">
        Découvrez comment LuminaHR transforme vos défis RH en avantages
        concurrentiels
      </p>
    </div>

    <!-- Section Problèmes -->
    <div class="problems-section" data-aos="fade-up" data-aos-delay="200">
      <div class="section-intro">
        <h3 class="problems-title">
          <span class="problem-emoji">😰</span>
          Les défis RH que vous connaissez trop bien
        </h3>
        <p class="problems-subtitle">
          Ces problèmes vous font perdre du temps, de l'argent et de
          l'efficacité
        </p>
      </div>

      <div class="problems-grid">
        <div
          class="problem-card"
          *ngFor="let problem of painPoints; let i = index"
          [attr.data-aos]="'fade-up'"
          [attr.data-aos-delay]="300 + i * 100"
          (mouseenter)="onCardHover($event)"
          (mouseleave)="onCardLeave($event)"
        >
          <div class="card-icon problem-icon">{{ problem.icon }}</div>
          <div class="card-content">
            <h4 class="card-title">{{ problem.title }}</h4>
            <p class="card-description">{{ problem.description }}</p>
            <div class="impact-badge">
              <span class="impact-text">{{ problem.impact }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Flèche de transformation -->
    <div class="transformation-section" data-aos="fade-up" data-aos-delay="600">
      <div class="transformation-arrow">
        <div class="arrow-circle">
          <i class="fas fa-arrow-down"></i>
        </div>
        <div class="transformation-text">
          <span>LuminaHR transforme</span>
        </div>
      </div>
    </div>

    <!-- Section Solutions -->
    <div class="solutions-section" data-aos="fade-up" data-aos-delay="800">
      <div class="section-intro">
        <h3 class="solutions-title">
          <span class="solution-emoji">🚀</span>
          La solution LuminaHR
        </h3>
        <p class="solutions-subtitle">
          Une plateforme moderne qui résout tous vos défis RH
        </p>
      </div>

      <div class="solutions-grid">
        <div
          class="solution-card"
          *ngFor="let solution of solutions; let i = index"
          [attr.data-aos]="'fade-up'"
          [attr.data-aos-delay]="900 + i * 100"
          (mouseenter)="onCardHover($event)"
          (mouseleave)="onCardLeave($event)"
        >
          <div class="card-icon solution-icon">{{ solution.icon }}</div>
          <div class="card-content">
            <h4 class="card-title">{{ solution.title }}</h4>
            <p class="card-description">{{ solution.description }}</p>
            <div class="benefit-badge">
              <span class="benefit-text">{{ solution.benefit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="cta-section" data-aos="fade-up" data-aos-delay="1200">
      <div class="cta-content">
        <h3 class="cta-title">Prêt à transformer votre gestion RH ?</h3>
        <p class="cta-subtitle">
          Rejoignez les 1,200+ entreprises qui ont déjà choisi LuminaHR pour
          optimiser leurs ressources humaines
        </p>
        <div class="cta-buttons">
          <button class="btn btn-primary btn-xl">
            <i class="fas fa-rocket"></i>
            <span>Démonstration gratuite</span>
          </button>
          <button class="btn btn-outline btn-xl">
            <i class="fas fa-play-circle"></i>
            <span>Voir LuminaHR en action</span>
          </button>
        </div>

        <!-- Garanties -->
        <div class="guarantees">
          <div class="guarantee-item">
            <i class="fas fa-check-circle"></i>
            <span>Démo personnalisée</span>
          </div>
          <div class="guarantee-item">
            <i class="fas fa-check-circle"></i>
            <span>Sans engagement</span>
          </div>
          <div class="guarantee-item">
            <i class="fas fa-check-circle"></i>
            <span>Support expert inclus</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
