import { Component, OnInit } from '@angular/core';
import * as AOS from 'aos';

@Component({
  selector: 'app-problem-solution',
  templateUrl: './problem-solution.component.html',
  styleUrls: ['./problem-solution.component.scss'],
})
export class ProblemSolutionComponent implements OnInit {

  ngOnInit(): void {
    // Configuration AOS
    AOS.init({
      duration: 800,
      easing: 'ease-out-cubic',
      once: true,
      offset: 100,
    });
  }

  ngAfterViewInit(): void {
    this.initAnimations();
  }

  private initAnimations(): void {
    // Animation des cartes problèmes avec effet de cascade
    gsap.fromTo(
      '.problem-card',
      {
        y: 50,
        opacity: 0,
        scale: 0.9,
      },
      {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.6,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
          trigger: '.problems-grid',
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Animation des cartes solutions avec effet de rebond
    gsap.fromTo(
      '.solution-card',
      {
        x: 100,
        opacity: 0,
        rotation: 5,
      },
      {
        x: 0,
        opacity: 1,
        rotation: 0,
        duration: 0.8,
        stagger: 0.15,
        ease: 'back.out(1.7)',
        scrollTrigger: {
          trigger: '.solutions-grid',
          start: 'top 80%',
          toggleActions: 'play none none reverse',
        },
      }
    );

    // Animation de la flèche de transformation
    gsap.fromTo(
      '.transformation-arrow',
      {
        scale: 0,
        rotation: -180,
      },
      {
        scale: 1,
        rotation: 0,
        duration: 1,
        ease: 'elastic.out(1, 0.5)',
        scrollTrigger: {
          trigger: '.transformation-section',
          start: 'top 70%',
          toggleActions: 'play none none reverse',
        },
      }
    );
  }

  // Animation au survol des cartes
  onCardHover(event: MouseEvent): void {
    const card = event.currentTarget as HTMLElement;
    gsap.to(card, {
      y: -8,
      scale: 1.02,
      duration: 0.3,
      ease: 'power2.out',
    });
  }

  onCardLeave(event: MouseEvent): void {
    const card = event.currentTarget as HTMLElement;
    gsap.to(card, {
      y: 0,
      scale: 1,
      duration: 0.3,
      ease: 'power2.out',
    });
  }
}
