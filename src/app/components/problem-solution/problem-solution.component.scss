/* === SECTION PROBLEM/SOLUTION LUMINA HR === */
.lumina-problem-solution {
  padding: var(--spacing-24) 0;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
  position: relative;
  overflow: hidden;

  @media (max-width: 768px) {
    padding: var(--spacing-16) 0;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

/* === EN-TÊTE DE SECTION === */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-20);

  .section-title {
    font-size: var(--text-5xl);
    font-weight: var(--font-black);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: var(--text-3xl);
    }

    .text-gradient {
      background: var(--gradient-fusion);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .section-subtitle {
    font-size: var(--text-xl);
    color: var(--gray-600);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: var(--text-lg);
    }
  }
}

/* === SECTION PROBLÈMES === */
.problems-section {
  margin-bottom: var(--spacing-16);

  .section-intro {
    text-align: center;
    margin-bottom: var(--spacing-12);

    .problems-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--spacing-4);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);

      @media (max-width: 768px) {
        font-size: var(--text-2xl);
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .problem-emoji {
        font-size: var(--text-5xl);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }
    }

    .problems-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .problems-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);
    }
  }
}

/* === CARTES PROBLÈMES === */
.problem-card {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #ef4444, #f97316);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: #ef4444;
  }

  .card-icon {
    font-size: var(--text-4xl);
    margin-bottom: var(--spacing-4);

    &.problem-icon {
      filter: grayscale(20%);
    }
  }

  .card-content {
    .card-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--spacing-3);
    }

    .card-description {
      font-size: var(--text-base);
      color: var(--gray-600);
      line-height: 1.6;
      margin-bottom: var(--spacing-4);
    }

    .impact-badge {
      background: linear-gradient(135deg, #fef2f2, #fee2e2);
      border: 1px solid #fecaca;
      border-radius: var(--radius-lg);
      padding: var(--spacing-2) var(--spacing-4);

      .impact-text {
        font-size: var(--text-sm);
        color: #dc2626;
        font-weight: var(--font-medium);
      }
    }
  }
}
/* === SECTION TRANSFORMATION === */
.transformation-section {
  display: flex;
  justify-content: center;
  margin: var(--spacing-12) 0;

  .transformation-arrow {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-3);

    .arrow-circle {
      width: 80px;
      height: 80px;
      background: var(--gradient-fusion);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: var(--shadow-lg);
      animation: pulse 2s infinite;

      i {
        font-size: var(--text-2xl);
        color: var(--white);
      }
    }

    .transformation-text {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(10px);
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-2) var(--spacing-4);

      span {
        font-size: var(--text-sm);
        font-weight: var(--font-semibold);
        color: var(--gray-700);
      }
    }
  }
}

/* === SECTION SOLUTIONS === */
.solutions-section {
  margin-bottom: var(--spacing-16);

  .section-intro {
    text-align: center;
    margin-bottom: var(--spacing-12);

    .solutions-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--spacing-4);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-3);

      @media (max-width: 768px) {
        font-size: var(--text-2xl);
        flex-direction: column;
        gap: var(--spacing-2);
      }

      .solution-emoji {
        font-size: var(--text-5xl);

        @media (max-width: 768px) {
          font-size: var(--text-3xl);
        }
      }
    }

    .solutions-subtitle {
      font-size: var(--text-lg);
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .solutions-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-4);
    }
  }
}

/* === CARTES SOLUTIONS === */
.solution-card {
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  transition: var(--transition-all);
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-fusion);
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--seamless-blue);
  }

  .card-icon {
    font-size: var(--text-4xl);
    margin-bottom: var(--spacing-4);

    &.solution-icon {
      filter: brightness(1.1);
    }
  }

  .card-content {
    .card-title {
      font-size: var(--text-xl);
      font-weight: var(--font-bold);
      color: var(--gray-900);
      margin-bottom: var(--spacing-3);
    }

    .card-description {
      font-size: var(--text-base);
      color: var(--gray-600);
      line-height: 1.6;
      margin-bottom: var(--spacing-4);
    }

    .benefit-badge {
      background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
      border: 1px solid #bae6fd;
      border-radius: var(--radius-lg);
      padding: var(--spacing-2) var(--spacing-4);

      .benefit-text {
        font-size: var(--text-sm);
        color: #0369a1;
        font-weight: var(--font-medium);
      }
    }
  }
}

/* === SECTION CTA === */
.cta-section {
  background: linear-gradient(
    135deg,
    var(--gradient-fusion),
    var(--gradient-seamless)
  );
  border-radius: var(--radius-3xl);
  padding: var(--spacing-12);
  text-align: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    z-index: 1;
  }

  .cta-content {
    position: relative;
    z-index: 2;

    .cta-title {
      font-size: var(--text-4xl);
      font-weight: var(--font-black);
      color: var(--white);
      margin-bottom: var(--spacing-4);

      @media (max-width: 768px) {
        font-size: var(--text-2xl);
      }
    }

    .cta-subtitle {
      font-size: var(--text-xl);
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: var(--spacing-8);
      max-width: 600px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.6;

      @media (max-width: 768px) {
        font-size: var(--text-lg);
      }
    }

    .cta-buttons {
      display: flex;
      gap: var(--spacing-4);
      justify-content: center;
      margin-bottom: var(--spacing-8);

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
      }

      .btn {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-2);
        padding: var(--spacing-4) var(--spacing-8);
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        border-radius: var(--radius-xl);
        text-decoration: none;
        transition: var(--transition-all);
        white-space: nowrap;

        &.btn-primary {
          background: var(--white);
          color: var(--seamless-blue);
          border: none;
          box-shadow: var(--shadow-lg);

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            background: var(--gray-50);
          }
        }

        &.btn-outline {
          background: rgba(255, 255, 255, 0.1);
          color: var(--white);
          border: 2px solid rgba(255, 255, 255, 0.3);
          backdrop-filter: blur(10px);

          &:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
          }
        }

        i {
          font-size: var(--text-xl);
        }
      }
    }

    .guarantees {
      display: flex;
      gap: var(--spacing-6);
      justify-content: center;
      flex-wrap: wrap;

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-3);
      }

      .guarantee-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        color: rgba(255, 255, 255, 0.9);
        font-size: var(--text-sm);
        font-weight: var(--font-medium);

        i {
          color: var(--seamless-green);
          font-size: var(--text-base);
        }
      }
    }
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .problems-grid,
  .solutions-grid {
    grid-template-columns: 1fr;
  }

  .problem-card,
  .solution-card {
    padding: var(--spacing-4);

    .card-icon {
      font-size: var(--text-3xl);
    }

    .card-content {
      .card-title {
        font-size: var(--text-lg);
      }

      .card-description {
        font-size: var(--text-sm);
      }
    }
  }

  .transformation-section {
    .transformation-arrow {
      .arrow-circle {
        width: 60px;
        height: 60px;

        i {
          font-size: var(--text-lg);
        }
      }
    }
  }
}
