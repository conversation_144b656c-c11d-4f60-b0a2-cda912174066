/* === SECTION SÉCURITÉ === */
.seamless-security {
  padding: 120px 0;
  background: white;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      circle at 80% 20%,
      rgba(59, 130, 246, 0.03) 0%,
      transparent 50%
    );
    z-index: 1;
  }

  .container {
    position: relative;
    z-index: 2;
  }
}

/* === EXPANSION GLOBALE === */
.global-expansion {
  margin-bottom: 120px;

  .expansion-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .expansion-text {
    .section-badge {
      display: inline-block;
      background: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 24px;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: #0f172a;
      margin-bottom: 24px;
      line-height: 1.2;
      letter-spacing: -0.02em;

      @media (max-width: 1024px) {
        font-size: 2.25rem;
      }

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: #64748b;
      max-width: 500px;
      margin-bottom: 32px;

      @media (max-width: 1024px) {
        margin: 0 auto 32px;
      }

      @media (max-width: 768px) {
        font-size: 1rem;
        line-height: 1.5;
      }
    }

    .security-features {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #374151;
        font-weight: 500;

        svg {
          color: #3b82f6;
          flex-shrink: 0;
        }

        span {
          font-size: 16px;
        }
      }

      @media (max-width: 768px) {
        align-items: center;
      }
    }
  }

  .expansion-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .world-map {
      .map-image {
        width: 100%;
        max-width: 500px;
        height: auto;
      }
    }
  }
}

/* === SÉCURITÉ FORTRESS === */
.fortress-security {
  .security-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .security-text {
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 24px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .certifications {
    display: flex;
    justify-content: center;
    gap: 60px;

    @media (max-width: 768px) {
      gap: 40px;
    }

    @media (max-width: 480px) {
      flex-direction: column;
      gap: 30px;
    }

    .cert-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .cert-badge {
        width: 80px;
        height: 80px;
        background: var(--gray-50);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);

        .cert-logo {
          height: 50px;
          width: auto;
        }
      }

      .cert-text {
        text-align: center;

        h3 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 4px;
        }

        p {
          font-size: 1rem;
          color: var(--gray-600);
          margin: 0;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .security-section {
    padding: 60px 0;
  }

  .global-expansion {
    margin-bottom: 80px;
  }

  .fortress-security .security-text {
    margin-bottom: 40px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
