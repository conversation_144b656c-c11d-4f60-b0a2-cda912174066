@use "./abstracts/" as *;
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap");

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: "Inter", sans-serif;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  width: 100%;
  height: 100%;
  font-family: "Inter", sans-serif;
}

input,
textarea,
select,
button {
  font-family: "Inter", sans-serif;
}

canvas {
  font-family: "Inter", sans-serif !important;
}

button {
  background: transparent;
}

a {
  text-decoration: none;
  color: inherit;
}

input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
  display: none;
}

/* Disable default styling for password field */
input[type="password"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.container {
  max-width: 1500px;
  margin: 0 auto;
}

.fadeIn {
  animation: fadeIn 0.35s forwards;
}

%fadeOpacity {
  animation: fadeIn var(--transition-1) forwards;
}

.fade-opacity {
  @extend %fadeOpacity;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(-5px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
