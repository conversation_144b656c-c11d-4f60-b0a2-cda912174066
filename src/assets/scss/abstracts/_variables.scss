// === SYSTÈME DE COULEURS PROFESSIONNEL ===

// Couleurs principales - Palette bleue sophistiquée
$primary: #2563eb; // Bleu principal moderne
$primary-50: #eff6ff; // Bleu très clair
$primary-100: #dbeafe; // Bleu clair
$primary-200: #bfdbfe; // Bleu léger
$primary-300: #93c5fd; // Bleu moyen clair
$primary-400: #60a5fa; // Bleu moyen
$primary-500: #3b82f6; // Bleu standard
$primary-600: #2563eb; // Bleu principal (notre couleur)
$primary-700: #1d4ed8; // Bleu foncé
$primary-800: #1e40af; // Bleu très foncé
$primary-900: #1e3a8a; // Bleu profond

// Couleurs neutres modernes
$neutral-50: #fafafa; // Blanc cassé
$neutral-100: #f5f5f5; // Gris très clair
$neutral-200: #e5e5e5; // Gris clair
$neutral-300: #d4d4d4; // Gris léger
$neutral-400: #a3a3a3; // Gris moyen
$neutral-500: #737373; // Gris standard
$neutral-600: #525252; // Gris foncé
$neutral-700: #404040; // Gris très foncé
$neutral-800: #262626; // Gris profond
$neutral-900: #171717; // Presque noir

// Couleurs sémantiques
$success: #059669; // Vert succès moderne
$success-light: #d1fae5; // Vert succès clair
$warning: #d97706; // Orange attention
$warning-light: #fed7aa; // Orange clair
$error: #dc2626; // Rouge erreur
$error-light: #fecaca; // Rouge clair
$info: #0891b2; // Cyan information
$info-light: #cffafe; // Cyan clair

// === TYPOGRAPHIE MODERNE ===
$font-family-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
  sans-serif;
$font-family-heading: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
  sans-serif;

// Tailles de police fluides
$text-xs: 0.75rem; // 12px
$text-sm: 0.875rem; // 14px
$text-base: 1rem; // 16px
$text-lg: 1.125rem; // 18px
$text-xl: 1.25rem; // 20px
$text-2xl: 1.5rem; // 24px
$text-3xl: 1.875rem; // 30px
$text-4xl: 2.25rem; // 36px
$text-5xl: 3rem; // 48px
$text-6xl: 3.75rem; // 60px

// Poids de police
$font-light: 300;
$font-normal: 400;
$font-medium: 500;
$font-semibold: 600;
$font-bold: 700;
$font-extrabold: 800;

// === ESPACEMENTS COHÉRENTS ===
$spacing-1: 0.25rem; // 4px
$spacing-2: 0.5rem; // 8px
$spacing-3: 0.75rem; // 12px
$spacing-4: 1rem; // 16px
$spacing-5: 1.25rem; // 20px
$spacing-6: 1.5rem; // 24px
$spacing-8: 2rem; // 32px
$spacing-10: 2.5rem; // 40px
$spacing-12: 3rem; // 48px
$spacing-16: 4rem; // 64px
$spacing-20: 5rem; // 80px
$spacing-24: 6rem; // 96px

// === BORDER RADIUS MODERNE ===
$radius-sm: 0.375rem; // 6px
$radius: 0.5rem; // 8px
$radius-md: 0.75rem; // 12px
$radius-lg: 1rem; // 16px
$radius-xl: 1.5rem; // 24px
$radius-2xl: 2rem; // 32px
$radius-full: 9999px; // Cercle parfait

// === OMBRES ÉLÉGANTES ===
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
$shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

// Ombres colorées pour les éléments interactifs
$shadow-primary: 0 10px 15px -3px rgba(37, 99, 235, 0.1),
  0 4px 6px -2px rgba(37, 99, 235, 0.05);
$shadow-primary-lg: 0 20px 25px -5px rgba(37, 99, 235, 0.1),
  0 10px 10px -5px rgba(37, 99, 235, 0.04);

// === TRANSITIONS FLUIDES ===
$transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
$transition-slower: 500ms cubic-bezier(0.4, 0, 0.2, 1);

// === BREAKPOINTS RESPONSIVE ===
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;
$breakpoint-2xl: 1536px;

// === CSS CUSTOM PROPERTIES MODERNES ===
:root {
  // Couleurs principales
  --primary-50: #{$primary-50};
  --primary-100: #{$primary-100};
  --primary-200: #{$primary-200};
  --primary-300: #{$primary-300};
  --primary-400: #{$primary-400};
  --primary-500: #{$primary-500};
  --primary-600: #{$primary-600};
  --primary-700: #{$primary-700};
  --primary-800: #{$primary-800};
  --primary-900: #{$primary-900};
  --primary: #{$primary};

  // Couleurs neutres
  --neutral-50: #{$neutral-50};
  --neutral-100: #{$neutral-100};
  --neutral-200: #{$neutral-200};
  --neutral-300: #{$neutral-300};
  --neutral-400: #{$neutral-400};
  --neutral-500: #{$neutral-500};
  --neutral-600: #{$neutral-600};
  --neutral-700: #{$neutral-700};
  --neutral-800: #{$neutral-800};
  --neutral-900: #{$neutral-900};

  // Couleurs sémantiques
  --success: #{$success};
  --success-light: #{$success-light};
  --warning: #{$warning};
  --warning-light: #{$warning-light};
  --error: #{$error};
  --error-light: #{$error-light};
  --info: #{$info};
  --info-light: #{$info-light};

  // Typographie
  --font-family-primary: #{$font-family-primary};
  --font-family-heading: #{$font-family-heading};

  // Tailles de texte
  --text-xs: #{$text-xs};
  --text-sm: #{$text-sm};
  --text-base: #{$text-base};
  --text-lg: #{$text-lg};
  --text-xl: #{$text-xl};
  --text-2xl: #{$text-2xl};
  --text-3xl: #{$text-3xl};
  --text-4xl: #{$text-4xl};
  --text-5xl: #{$text-5xl};
  --text-6xl: #{$text-6xl};

  // Espacements
  --spacing-1: #{$spacing-1};
  --spacing-2: #{$spacing-2};
  --spacing-3: #{$spacing-3};
  --spacing-4: #{$spacing-4};
  --spacing-5: #{$spacing-5};
  --spacing-6: #{$spacing-6};
  --spacing-8: #{$spacing-8};
  --spacing-10: #{$spacing-10};
  --spacing-12: #{$spacing-12};
  --spacing-16: #{$spacing-16};
  --spacing-20: #{$spacing-20};
  --spacing-24: #{$spacing-24};

  // Border radius
  --radius-sm: #{$radius-sm};
  --radius: #{$radius};
  --radius-md: #{$radius-md};
  --radius-lg: #{$radius-lg};
  --radius-xl: #{$radius-xl};
  --radius-2xl: #{$radius-2xl};
  --radius-full: #{$radius-full};

  // Ombres
  --shadow-xs: #{$shadow-xs};
  --shadow-sm: #{$shadow-sm};
  --shadow: #{$shadow};
  --shadow-md: #{$shadow-md};
  --shadow-lg: #{$shadow-lg};
  --shadow-xl: #{$shadow-xl};
  --shadow-2xl: #{$shadow-2xl};
  --shadow-inner: #{$shadow-inner};
  --shadow-primary: #{$shadow-primary};
  --shadow-primary-lg: #{$shadow-primary-lg};

  // Transitions
  --transition-fast: #{$transition-fast};
  --transition-base: #{$transition-base};
  --transition-slow: #{$transition-slow};
  --transition-slower: #{$transition-slower};

  // Breakpoints (pour JS)
  --breakpoint-sm: #{$breakpoint-sm};
  --breakpoint-md: #{$breakpoint-md};
  --breakpoint-lg: #{$breakpoint-lg};
  --breakpoint-xl: #{$breakpoint-xl};
  --breakpoint-2xl: #{$breakpoint-2xl};
}
