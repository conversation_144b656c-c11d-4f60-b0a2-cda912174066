{"name": "compluse-hr-landing-page", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "dev:ssr": "ng run compluse-hr-landing-page:serve-ssr", "serve:ssr": "node dist/compluse-hr-landing-page/server/main.js", "build:ssr": "ng build && ng run compluse-hr-landing-page:server", "prerender": "ng run compluse-hr-landing-page:prerender"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/cdk": "16.2.14", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/material": "16.2.14", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/platform-server": "^16.2.0", "@angular/router": "^16.2.0", "@nguniversal/express-engine": "^16.2.0", "angular-calendar": "^0.31.1", "aos": "^2.3.4", "chart.js": "^3.8.0", "date-fns": "^3.6.0", "express": "^4.15.2", "gsap": "^3.13.0", "icon": "link:@angular/material/icon", "ng2-charts": "^3.1.0", "ngx-bootstrap": "^19.0.2", "ngx-loading": "^17.0.0", "ngx-pagination": "^6.0.3", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.14", "@angular/cli": "^16.2.14", "@angular/compiler-cli": "^16.2.0", "@nguniversal/builders": "^16.2.0", "@types/aos": "^3.0.7", "@types/express": "^4.17.0", "@types/jasmine": "~4.3.0", "@types/node": "^16.11.7", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}