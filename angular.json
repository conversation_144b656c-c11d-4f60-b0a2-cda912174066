{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"compluse-hr-landing-page": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/compluse-hr-landing-page/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/pink-bluegrey.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "30kb", "maximumError": "30kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "compluse-hr-landing-page:build:production"}, "development": {"browserTarget": "compluse-hr-landing-page:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "compluse-hr-landing-page:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/pink-bluegrey.css", "src/styles.scss"], "scripts": []}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/compluse-hr-landing-page/server", "main": "server.ts", "tsConfig": "tsconfig.server.json", "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media"}, "development": {"buildOptimizer": false, "optimization": false, "sourceMap": true, "extractLicenses": false, "vendorChunk": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@nguniversal/builders:ssr-dev-server", "configurations": {"development": {"browserTarget": "compluse-hr-landing-page:build:development", "serverTarget": "compluse-hr-landing-page:server:development"}, "production": {"browserTarget": "compluse-hr-landing-page:build:production", "serverTarget": "compluse-hr-landing-page:server:production"}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@nguniversal/builders:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "compluse-hr-landing-page:build:production", "serverTarget": "compluse-hr-landing-page:server:production"}, "development": {"browserTarget": "compluse-hr-landing-page:build:development", "serverTarget": "compluse-hr-landing-page:server:development"}}, "defaultConfiguration": "production"}}}}, "cli": {"analytics": "f7628607-bcfc-4860-8941-ac3096fc1bd5"}}